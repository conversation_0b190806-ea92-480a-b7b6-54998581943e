-- Function to create a scheduler trigger for Informed tasks
CREATE OR REPLACE FUNCTION public.fn_create_informed_scheduler_trigger()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create a scheduled task entry in t_scheduler if it doesn't exist
  INSERT INTO public.t_scheduler (
    name,
    description,
    schedule,
    enabled,
    last_run,
    created_at,
    updated_at
  ) VALUES (
    'informed_scheduler',
    'Scheduler for Informed Repricer tasks',
    '30 6,12,18 * * *', -- 6:30 AM, 12:30 PM, 6:30 PM every day
    true,
    NULL,
    NOW(),
    NOW()
  )
  ON CONFLICT (name) DO UPDATE SET
    schedule = EXCLUDED.schedule,
    updated_at = NOW();

  -- Create a function to handle the scheduled task
  EXECUTE $FUNC$
  CREATE OR REPLACE FUNCTION public.fn_handle_informed_scheduler()
  RETURNS TRIGGER AS $TRIG$
  BEGIN
    -- Enqueue a task to run the Informed process
    PERFORM public.fn_enqueue_informed_tasks(
      'run_informed_process',
      '{}',
      0  -- Run immediately
    );

    RETURN NEW;
  END;
  $TRIG$ LANGUAGE plpgsql;
  $FUNC$;

  -- Drop the trigger if it already exists
  DROP TRIGGER IF EXISTS tr_informed_scheduler ON public.t_scheduler;

  -- Create a new trigger that runs when the scheduler is updated
  EXECUTE $TRIG$
  CREATE TRIGGER tr_informed_scheduler
  AFTER UPDATE OF last_run ON public.t_scheduler
  FOR EACH ROW
  WHEN (NEW.name = 'informed_scheduler' AND NEW.enabled = true)
  EXECUTE FUNCTION public.fn_handle_informed_scheduler();
  $TRIG$;

  RAISE NOTICE 'Created Informed scheduler trigger';
END;
$$;
