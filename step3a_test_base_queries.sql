-- Step 3a: Test the base queries to see which part is slow
-- Run these one at a time to identify the bottleneck

-- Test 1: Check if the config cache is working
SELECT * FROM mv_config_cache;

-- Test 2: Test basic sdasins query (should be fast)
SELECT COUNT(*) 
FROM t_sdasins ts
WHERE ts.fba_uploaded_at IS NOT NULL;

-- Test 3: Test with one join (v_sdasins_avg_carrying_costs might be slow)
SELECT COUNT(*) 
FROM t_sdasins ts
JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
WHERE ts.fba_uploaded_at IS NOT NULL;

-- Test 4: Test with mps join
SELECT COUNT(*) 
FROM t_sdasins ts
JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
JOIN t_mps tm ON ts.mps_id = tm.id
WHERE ts.fba_uploaded_at IS NOT NULL;

-- Test 5: Test with all joins
SELECT COUNT(*) 
FROM t_sdasins ts
JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
JOIN t_mps tm ON ts.mps_id = tm.id
JOIN t_plastics tp ON tm.plastic_id = tp.id
LEFT JOIN v_sdasins vs ON ts.id = vs.id
WHERE ts.fba_uploaded_at IS NOT NULL;

-- Test 6: Check if v_sdasins_avg_carrying_costs is the problem
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) FROM v_sdasins_avg_carrying_costs;
