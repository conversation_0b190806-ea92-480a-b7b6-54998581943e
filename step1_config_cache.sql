-- Step 1: Create config cache (small, fast operation)
-- Run this first - it should complete quickly

CREATE MATERIALIZED VIEW IF NOT EXISTS mv_config_cache AS
SELECT 
    MAX(CASE WHEN key = 'fba_min_profit' THEN value::numeric END) as fba_min_profit,
    MAX(CASE WHEN key = 'fba_s_and_h_fees' THEN value::numeric END) as fba_s_and_h_fees,
    MAX(CASE WHEN key = 'fbm_fees' THEN value::numeric END) as fbm_fees
FROM t_config 
WHERE key IN ('fba_min_profit', 'fba_s_and_h_fees', 'fbm_fees');

-- Create index for faster access
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_config_cache_single_row ON mv_config_cache ((1));

-- Test the config cache
SELECT * FROM mv_config_cache;
