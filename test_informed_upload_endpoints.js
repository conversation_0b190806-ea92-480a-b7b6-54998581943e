/**
 * Test script to find the correct Informed upload endpoint
 */

import fetch from 'node-fetch';
import FormData from 'form-data';

const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const BASE_URL = 'https://api.informed.co';

// Test different potential upload endpoints
const POTENTIAL_ENDPOINTS = [
    '/upload',
    '/uploads',
    '/pricing/upload',
    '/pricing/import',
    '/data/upload',
    '/data/import',
    '/files/upload',
    '/inventory/upload',
    '/inventory/import'
];

async function testEndpoint(endpoint) {
    console.log(`\nTesting endpoint: ${BASE_URL}${endpoint}`);
    
    try {
        // Create a simple test CSV
        const testCsv = 'sku,marketplace_id,cost,currency,min_price,max_price,map_price,listing_type,strategy_id,managed,dateadded\nTEST-SKU,9432,10.00,USD,15.00,50.00,20.00,Amazon FBA,1,1,2024-01-01';
        
        // Test with form data
        const formData = new FormData();
        formData.append('file', testCsv, {
            filename: 'test_upload.csv',
            contentType: 'text/csv'
        });
        
        const response = await fetch(`${BASE_URL}${endpoint}`, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData.getHeaders()
            },
            body: formData
        });
        
        console.log(`Status: ${response.status} ${response.statusText}`);
        
        const responseText = await response.text();
        console.log(`Response: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);
        
        return {
            endpoint,
            status: response.status,
            response: responseText
        };
        
    } catch (error) {
        console.log(`Error: ${error.message}`);
        return {
            endpoint,
            error: error.message
        };
    }
}

async function testAllEndpoints() {
    console.log('Testing Informed API upload endpoints...\n');
    
    const results = [];
    
    for (const endpoint of POTENTIAL_ENDPOINTS) {
        const result = await testEndpoint(endpoint);
        results.push(result);
        
        // Wait a bit between requests to be nice to the API
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n=== SUMMARY ===');
    results.forEach(result => {
        if (result.error) {
            console.log(`${result.endpoint}: ERROR - ${result.error}`);
        } else {
            console.log(`${result.endpoint}: ${result.status} - ${result.response.substring(0, 50)}...`);
        }
    });
    
    // Look for successful responses (2xx status codes)
    const successful = results.filter(r => !r.error && r.status >= 200 && r.status < 300);
    if (successful.length > 0) {
        console.log('\nSuccessful endpoints:');
        successful.forEach(r => console.log(`  ${r.endpoint}: ${r.status}`));
    }
    
    // Look for endpoints that don't return 404 (might be valid but need different data)
    const notFound = results.filter(r => !r.error && r.status !== 404);
    if (notFound.length > 0) {
        console.log('\nEndpoints that exist (non-404):');
        notFound.forEach(r => console.log(`  ${r.endpoint}: ${r.status}`));
    }
}

// Also test if we can get API documentation or available endpoints
async function testApiInfo() {
    console.log('\nTesting for API documentation endpoints...');
    
    const infoEndpoints = [
        '/',
        '/docs',
        '/api',
        '/help',
        '/endpoints',
        '/swagger',
        '/openapi'
    ];
    
    for (const endpoint of infoEndpoints) {
        try {
            const response = await fetch(`${BASE_URL}${endpoint}`, {
                method: 'GET',
                headers: {
                    'x-api-key': API_KEY,
                    'Accept': 'application/json'
                }
            });
            
            if (response.status !== 404) {
                console.log(`${endpoint}: ${response.status} - ${response.statusText}`);
                const text = await response.text();
                if (text.length < 500) {
                    console.log(`  Response: ${text}`);
                }
            }
        } catch (error) {
            // Ignore errors for info endpoints
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

async function main() {
    await testApiInfo();
    await testAllEndpoints();
}

main().catch(console.error);
