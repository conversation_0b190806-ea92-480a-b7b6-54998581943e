-- Step 3: Create a simpler optimized view without materialized disc counts
-- This approach uses the config cache but avoids the expensive disc count materialization

CREATE OR REPLACE VIEW v_informed_upload_simple_optimized AS
WITH config AS (
    SELECT * FROM mv_config_cache LIMIT 1
),
fba_data AS (
    SELECT 
        ts.fba_sku as sku,
        '9432'::text as marketplace_id,
        -- Calculate cost with config values from cache
        (GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fba, 0.0) + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85
        ) - 0.01)::numeric as cost,
        'USD'::text as currency,
        -- Calculate min price
        GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fba, 0.0) + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85
        ) as min_price,
        -- Calculate max price
        COALESCE(
            ts.override_amazon_max_price,
            tm.val_override_max_amazon_price,
            tp.val_max_amazon_price,
            tp.val_msrp
        ) as max_price,
        -- Map price
        COALESCE(tm.val_override_map_price, tp.val_map_price) as map_price,
        'Amazon FBA'::text as listing_type,
        vs.fba_informed_strategy_id as strategy_id,
        '1'::text as managed,
        now() as dateadded
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    CROSS JOIN config c
    WHERE ts.fba_uploaded_at IS NOT NULL
      AND vs.fba_informed_strategy_id IS NOT NULL
),
fbm_data AS (
    SELECT 
        ts.fbm_sku as sku,
        '9432'::text as marketplace_id,
        -- Calculate cost with config values from cache
        (GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fbm, 0.0) + c.fbm_fees) / 0.85
        ) - 0.01)::numeric as cost,
        'USD'::text as currency,
        -- Calculate min price
        GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fbm, 0.0) + c.fbm_fees) / 0.85
        ) as min_price,
        -- Calculate max price
        COALESCE(
            ts.override_amazon_max_price,
            tm.val_override_max_amazon_price,
            tp.val_max_amazon_price,
            tp.val_msrp
        ) as max_price,
        -- Map price
        COALESCE(tm.val_override_map_price, tp.val_map_price) as map_price,
        'Amazon FBM'::text as listing_type,
        vs.fbm_informed_strategy_id as strategy_id,
        '1'::text as managed,
        now() as dateadded
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    CROSS JOIN config c
    WHERE ts.fbm_uploaded_at IS NOT NULL
      AND vs.fbm_informed_strategy_id IS NOT NULL
)
SELECT * FROM fba_data
UNION ALL
SELECT * FROM fbm_data;

-- Test the view
SELECT COUNT(*) as total_records FROM v_informed_upload_simple_optimized;
