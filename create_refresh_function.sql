-- Create a stored procedure to refresh the materialized view
-- This needs to be run in Supabase SQL Editor with elevated privileges

CREATE OR REPLACE FUNCTION refresh_mv_sdasin_avg_carrying_cost_fba()
RETURNS TABLE(
  record_count bigint,
  refresh_time timestamp with time zone
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  start_time timestamp with time zone;
  end_time timestamp with time zone;
  row_count bigint;
BEGIN
  start_time := now();
  
  -- Refresh the materialized view
  REFRESH MATERIALIZED VIEW mv_sdasin_avg_carrying_cost_fba;
  
  -- Get the count of records
  SELECT count(*) INTO row_count FROM mv_sdasin_avg_carrying_cost_fba;
  
  end_time := now();
  
  -- Return the results
  RETURN QUERY SELECT row_count, end_time;
END;
$$;

-- Grant execute permission to the service role
GRANT EXECUTE ON FUNCTION refresh_mv_sdasin_avg_carrying_cost_fba() TO service_role;
GRANT EXECUTE ON FUNCTION refresh_mv_sdasin_avg_carrying_cost_fba() TO authenticated;
