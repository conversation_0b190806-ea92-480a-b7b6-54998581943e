/**
 * Informed Report Downloader
 *
 * This script downloads reports from Informed Repricer API and saves them to the local file system.
 * It handles three report types:
 * 1. All Fields
 * 2. Competition Landscape
 * 3. Listings That Do Not Have The BuyBox
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Constants
const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH'; // Make sure this API key is correct
const BASE_URL = 'https://api.informed.co';
const REPORTS_DIR = path.join(__dirname, 'data', 'external data');
const REPORT_TYPES = [
    {
        type: 'All_Fields',
        reportType: 'All_Fields',
        filename: 'from_informed_all_fields.csv',
        tableName: 'it_infor_all_fields'
    },
    {
        type: 'Competition_Landscape',
        reportType: 'Competition_Landscape',
        filename: 'from_informed_competition_landscape.csv',
        tableName: 'it_infor_competition_landscape'
    },
    {
        type: 'Listings_That_Do_Not_Have_The_BuyBox',
        reportType: 'Listings_That_Do_Not_Have_The_BuyBox',
        filename: 'from_informed_no_buy_box.csv',
        tableName: 'it_infor_no_buy_box'
    }
];

// Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Ensure the reports directory exists
 */
function ensureReportsDirectory() {
    if (!fs.existsSync(REPORTS_DIR)) {
        fs.mkdirSync(REPORTS_DIR, { recursive: true });
        console.log(`Created directory: ${REPORTS_DIR}`);
    }
}

/**
 * Request a report from Informed API
 * @param {Object} report - Report configuration object
 * @returns {Promise<Object>} - Result of the request operation
 */
async function requestReport(report) {
    const url = `${BASE_URL}/reports/requestReport`;

    console.log(`Requesting ${report.type} report from ${url}`);

    try {
        const response = await axios({
            method: 'GET',
            url: url,
            params: {
                reportType: report.reportType
            },
            headers: {
                'x-api-key': API_KEY,
                'Accept': 'application/json'
            }
        });

        console.log(`Response for ${report.type}:`, JSON.stringify(response.data));

        if (response.data && (response.data.reportRequestID || response.data.ReportRequestID)) {
            const reportRequestID = response.data.reportRequestID || response.data.ReportRequestID;
            console.log(`Successfully requested ${report.type} report with ID: ${reportRequestID}`);
            return {
                success: true,
                report: report.type,
                reportRequestID: reportRequestID,
                message: `Successfully requested ${report.type} report`
            };
        } else {
            console.error(`Error requesting ${report.type} report: Invalid response`);
            console.error(`Response data:`, JSON.stringify(response.data));
            return {
                success: false,
                report: report.type,
                error: 'Invalid response from Informed API'
            };
        }
    } catch (error) {
        console.error(`Error requesting ${report.type} report:`, error.message);
        if (error.response) {
            console.error(`Response status:`, error.response.status);
            console.error(`Response data:`, JSON.stringify(error.response.data));
        }
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Check the status of a report request
 * @param {Object} report - Report configuration object
 * @param {string} reportRequestID - Report request ID
 * @returns {Promise<Object>} - Result of the status check operation
 */
async function checkReportStatus(report, reportRequestID) {
    const url = `${BASE_URL}/reports/requests/${reportRequestID}`;

    console.log(`Checking status of ${report.type} report with ID: ${reportRequestID}`);

    try {
        const response = await axios({
            method: 'GET',
            url: url,
            headers: {
                'x-api-key': API_KEY,
                'Accept': 'application/json'
            }
        });

        console.log(`Status response for ${report.type}:`, JSON.stringify(response.data));

        if (response.data) {
            const status = response.data.status || response.data.Status;

            // Check for download URL in different formats
            const downloadUrl = response.data.downloadUrl || response.data.DownloadUrl || response.data.DownloadLink || null;

            console.log(`Status of ${report.type} report with ID ${reportRequestID}: ${status}`);
            console.log(`Download URL: ${downloadUrl || 'Not available'}`);
            console.log(`Full response data: ${JSON.stringify(response.data, null, 2)}`);

            return {
                success: true,
                report: report.type,
                reportRequestID,
                status,
                downloadUrl,
                message: `Status of ${report.type} report: ${status}`
            };
        } else {
            console.error(`Error checking status of ${report.type} report: Invalid response`);
            console.error(`Response data:`, JSON.stringify(response.data));
            return {
                success: false,
                report: report.type,
                reportRequestID,
                error: 'Invalid response from Informed API'
            };
        }
    } catch (error) {
        console.error(`Error checking status of ${report.type} report:`, error.message);
        if (error.response) {
            console.error(`Response status:`, error.response.status);
            console.error(`Response data:`, JSON.stringify(error.response.data));
        }
        return {
            success: false,
            report: report.type,
            reportRequestID,
            error: error.message
        };
    }
}

/**
 * Download a report from Informed API
 * @param {Object} report - Report configuration object
 * @param {string} downloadUrl - URL to download the report
 * @returns {Promise<Object>} - Result of the download operation
 */
async function downloadReport(report, downloadUrl) {
    const filePath = path.join(REPORTS_DIR, report.filename);

    console.log(`Downloading ${report.type} report from ${downloadUrl}`);

    try {
        console.log(`Attempting to download from URL: ${downloadUrl}`);

        // First try with Accept: text/csv
        let response;
        try {
            response = await axios({
                method: 'GET',
                url: downloadUrl,
                responseType: 'stream',
                headers: {
                    'x-api-key': API_KEY,
                    'Accept': 'text/csv'
                }
            });
            console.log(`Successfully got response with Accept: text/csv`);
        } catch (csvError) {
            console.log(`Failed to download with Accept: text/csv - ${csvError.message}`);
            console.log(`Trying again with Accept: application/octet-stream`);

            // If that fails, try with Accept: application/octet-stream
            response = await axios({
                method: 'GET',
                url: downloadUrl,
                responseType: 'stream',
                headers: {
                    'x-api-key': API_KEY,
                    'Accept': 'application/octet-stream'
                }
            });
            console.log(`Successfully got response with Accept: application/octet-stream`);
        }

        const writer = fs.createWriteStream(filePath);

        return new Promise((resolve, reject) => {
            response.data.pipe(writer);

            writer.on('finish', () => {
                console.log(`Successfully downloaded ${report.type} report to ${filePath}`);

                // Check if the file is empty or too small
                const stats = fs.statSync(filePath);
                if (stats.size < 10) { // Arbitrary small size to check for essentially empty files
                    console.error(`Downloaded file for ${report.type} is too small (${stats.size} bytes)`);
                    reject({
                        success: false,
                        report: report.type,
                        error: 'Downloaded file is too small or empty'
                    });
                    return;
                }

                resolve({
                    success: true,
                    report: report.type,
                    filePath,
                    message: `Successfully downloaded ${report.type} report`
                });
            });

            writer.on('error', (err) => {
                console.error(`Error writing ${report.type} report to file:`, err);
                reject({
                    success: false,
                    report: report.type,
                    error: err.message
                });
            });
        });
    } catch (error) {
        console.error(`Error downloading ${report.type} report:`, error.message);
        if (error.response) {
            console.error(`Response status:`, error.response.status);
            console.error(`Response headers:`, JSON.stringify(error.response.headers));
        }
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Process a single report (request, wait for completion, download)
 * @param {Object} report - Report configuration object
 * @param {number} maxRetries - Maximum number of retries for checking status
 * @param {number} retryInterval - Interval between retries in milliseconds
 * @returns {Promise<Object>} - Result of the operation
 */
async function processReport(report, maxRetries = 30, retryInterval = 10000) {
    try {
        // Step 1: Request the report
        const requestResult = await requestReport(report);

        if (!requestResult.success) {
            return {
                success: false,
                report: report.type,
                error: requestResult.error || 'Failed to request report'
            };
        }

        const reportRequestID = requestResult.reportRequestID;

        // Step 2: Check status and wait for completion
        let statusResult = null;
        let retries = 0;

        while (retries < maxRetries) {
            // Wait before checking status
            await new Promise(resolve => setTimeout(resolve, retryInterval));

            statusResult = await checkReportStatus(report, reportRequestID);

            if (!statusResult.success) {
                return {
                    success: false,
                    report: report.type,
                    error: statusResult.error || 'Failed to check report status'
                };
            }

            // If report is complete, check for download URL
            if (statusResult.status === 'Complete' || statusResult.status === 'Completed' || statusResult.status === 'Complette') {
                // Check for download URL in different formats
                statusResult.downloadUrl = statusResult.downloadUrl || statusResult.DownloadUrl || statusResult.DownloadLink;

                if (statusResult.downloadUrl) {
                    console.log(`Report ${report.type} is complete and download URL is available: ${statusResult.downloadUrl}`);
                    console.log(`Breaking loop.`);
                    break;
                } else {
                    console.log(`Report ${report.type} is complete but download URL is not available. Waiting for download URL...`);

                    // If we've been waiting for a while and the status is Complete but no download URL,
                    // try to construct the download URL manually
                    if (retries > maxRetries / 2) {
                        console.log(`Attempting to construct download URL manually for ${report.type}...`);

                        // Construct a download URL based on the report request ID
                        const constructedUrl = `${BASE_URL}/reports/requests/${reportRequestID}/download`;
                        console.log(`Constructed URL: ${constructedUrl}`);

                        // Update the status result with the constructed URL
                        statusResult.downloadUrl = constructedUrl;
                        break;
                    }
                }
            }

            // If report has an error, return the error
            if (statusResult.status === 'Error' || statusResult.status === 'Failed') {
                return {
                    success: false,
                    report: report.type,
                    error: 'Report generation failed on Informed side'
                };
            }

            retries++;
            console.log(`Waiting for ${report.type} report to complete... (${retries}/${maxRetries})`);
        }

        if (!statusResult) {
            return {
                success: false,
                report: report.type,
                error: 'Failed to get report status'
            };
        }

        // Check for complete status in different formats
        const isComplete = statusResult.status === 'Complete' ||
                          statusResult.status === 'Completed' ||
                          statusResult.status === 'Complette';

        if (!isComplete) {
            return {
                success: false,
                report: report.type,
                error: `Report did not complete within the allowed time. Last status: ${statusResult.status}`
            };
        }

        // Check for download URL in different formats
        statusResult.downloadUrl = statusResult.downloadUrl ||
                                  statusResult.DownloadUrl ||
                                  statusResult.DownloadLink;

        if (!statusResult.downloadUrl) {
            console.log(`Report ${report.type} is complete but no download URL is available. Attempting to construct one...`);

            // Construct a download URL based on the report request ID as a last resort
            statusResult.downloadUrl = `${BASE_URL}/reports/requests/${reportRequestID}/download`;
            console.log(`Constructed URL: ${statusResult.downloadUrl}`);
        }

        // Step 3: Download the report
        const downloadResult = await downloadReport(report, statusResult.downloadUrl);

        return downloadResult;
    } catch (error) {
        console.error(`Error processing ${report.type} report:`, error);
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Download all reports from Informed
 * @param {number} maxRetries - Maximum number of retries for checking status
 * @param {number} retryInterval - Interval between retries in milliseconds
 * @returns {Promise<Array>} - Results of all download operations
 */
async function downloadAllReports(maxRetries = 30, retryInterval = 10000) {
    ensureReportsDirectory();

    const results = [];

    for (const report of REPORT_TYPES) {
        try {
            console.log(`Processing ${report.type} report...`);
            const result = await processReport(report, maxRetries, retryInterval);
            results.push(result);
        } catch (error) {
            console.error(`Error processing ${report.type} report:`, error);
            results.push({
                success: false,
                report: report.type,
                error: error.message
            });
        }
    }

    return results;
}

/**
 * Get the status of all reports
 * @returns {Promise<Array>} - Status of all reports
 */
async function getReportsStatus() {
    const status = [];

    for (const report of REPORT_TYPES) {
        const filePath = path.join(REPORTS_DIR, report.filename);
        let fileExists = false;
        let lastModified = null;
        let fileSize = 0;

        try {
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                fileExists = true;
                lastModified = stats.mtime;
                fileSize = stats.size;
            }

            // Get record count from database
            const { count, error } = await supabase
                .from(report.tableName)
                .select('*', { count: 'exact', head: true });

            status.push({
                name: report.type,
                filename: report.filename,
                exists: fileExists,
                lastUpdated: lastModified,
                fileSize,
                recordCount: count || 0,
                status: fileExists && fileSize > 0 ? 'OK' : 'Missing',
                tableName: report.tableName
            });
        } catch (error) {
            console.error(`Error getting status for ${report.type}:`, error);
            status.push({
                name: report.type,
                filename: report.filename,
                exists: fileExists,
                lastUpdated: lastModified,
                fileSize,
                recordCount: 0,
                status: 'Error',
                error: error.message,
                tableName: report.tableName
            });
        }
    }

    return status;
}

// Export functions for use in other modules
module.exports = {
    downloadAllReports,
    getReportsStatus,
    requestReport,
    checkReportStatus,
    processReport,
    REPORT_TYPES
};

// If this script is run directly, download all reports
if (require.main === module) {
    downloadAllReports()
        .then(results => {
            console.log('Download results:', results);
            process.exit(0);
        })
        .catch(error => {
            console.error('Error downloading reports:', error);
            process.exit(1);
        });
}
