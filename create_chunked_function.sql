-- Create the missing chunked function for large datasets

CREATE OR R<PERSON>LACE FUNCTION public.fn_fill_tu_informed_chunked(chunk_size integer DEFAULT 1000)
RETURNS TABLE(
    success boolean,
    total_records integer,
    chunks_processed integer,
    error_message text
) AS $$
DECLARE
    chunk_count integer := 0;
    total_count integer := 0;
    current_offset integer := 0;
    records_in_chunk integer;
BEGIN
    -- Get total count first
    SELECT COUNT(*) INTO total_count FROM v_informed_upload_minimal;
    
    -- Process in chunks
    LOOP
        -- Insert a chunk of data
        INSERT INTO tu_informed (
            sku,
            marketplace_id,
            cost,
            currency,
            min_price,
            max_price,
            map_price,
            listing_type,
            strategy_id,
            managed,
            dateadded
        )
        SELECT 
            sku,
            marketplace_id,
            cost,
            currency,
            min_price,
            max_price,
            COALESCE(map_price, 0) AS map_price,  -- Replace NULL with 0
            listing_type,
            strategy_id::bigint,  -- Convert to bigint
            managed,
            dateadded
        FROM v_informed_upload_minimal
        ORDER BY sku  -- Ensure consistent ordering
        LIMIT chunk_size
        OFFSET current_offset;
        
        -- Get the number of records inserted in this chunk
        GET DIAGNOSTICS records_in_chunk = ROW_COUNT;
        
        -- If no records were inserted, we're done
        IF records_in_chunk = 0 THEN
            EXIT;
        END IF;
        
        -- Update counters
        chunk_count := chunk_count + 1;
        current_offset := current_offset + chunk_size;
        
        -- Commit this chunk (in case of large datasets)
        COMMIT;
        
        -- Small delay to avoid overwhelming the system
        PERFORM pg_sleep(0.1);
        
    END LOOP;
    
    -- Return success result
    RETURN QUERY SELECT true, total_count, chunk_count, null::text;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error result
        RETURN QUERY SELECT false, 0, chunk_count, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
