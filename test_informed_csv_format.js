/**
 * Test different CSV formats for Informed API
 */

import fetch from 'node-fetch';
import FormData from 'form-data';

const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const UPLOAD_URL = 'https://api.informed.co/v1/feed';

// Test different CSV formats based on common Informed formats
const csvFormats = [
    {
        name: 'Standard format',
        csv: `sku,marketplace_id,cost,currency,min_price,max_price,map_price,listing_type,strategy_id,managed,dateadded
TEST-SKU-001,9432,10.00,USD,15.00,50.00,20.00,Amazon FBA,1,1,2024-01-01`
    },
    {
        name: 'Uppercase headers',
        csv: `SKU,MARKETPLACE_ID,COST,CURRENCY,MIN_PRICE,MAX_PRICE,MAP_PRICE,LISTING_TYPE,STRATEGY_ID,MANAGED,DATEADDED
TEST-SKU-001,9432,10.00,USD,15.00,50.00,20.00,Amazon FBA,1,1,2024-01-01`
    },
    {
        name: 'Minimal required fields',
        csv: `sku,marketplace_id,cost,currency,min_price,max_price
TEST-SKU-001,9432,10.00,USD,15.00,50.00`
    },
    {
        name: 'Tab-separated',
        csv: `sku\tmarketplace_id\tcost\tcurrency\tmin_price\tmax_price
TEST-SKU-001\t9432\t10.00\tUSD\t15.00\t50.00`
    },
    {
        name: 'Different field names',
        csv: `item_id,marketplace,cost_price,currency_code,minimum_price,maximum_price
TEST-SKU-001,9432,10.00,USD,15.00,50.00`
    },
    {
        name: 'Amazon-style format',
        csv: `seller-sku,marketplace-id,cost,currency,minimum-seller-allowed-price,maximum-seller-allowed-price
TEST-SKU-001,9432,10.00,USD,15.00,50.00`
    }
];

async function testFormat(format) {
    console.log(`\n=== Testing: ${format.name} ===`);
    console.log(`CSV content:\n${format.csv}\n`);
    
    try {
        const formData = new FormData();
        formData.append('file', Buffer.from(format.csv, 'utf8'), {
            filename: 'test_upload.csv',
            contentType: 'text/csv'
        });
        
        const response = await fetch(UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData.getHeaders()
            },
            body: formData
        });
        
        console.log(`Status: ${response.status} ${response.statusText}`);
        const responseText = await response.text();
        console.log(`Response: ${responseText}`);
        
        if (response.ok) {
            console.log('✅ SUCCESS! This format works!');
            return true;
        }
        
    } catch (error) {
        console.log(`Error: ${error.message}`);
    }
    
    return false;
}

async function testAllFormats() {
    console.log('Testing different CSV formats for Informed API...');
    
    for (const format of csvFormats) {
        const success = await testFormat(format);
        if (success) {
            break; // Stop on first success
        }
        
        // Wait a bit between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\nTesting complete.');
}

testAllFormats().catch(console.error);
