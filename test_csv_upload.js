/**
 * Test script to debug CSV upload to Informed API
 */

import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';

const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const UPLOAD_URL = 'https://api.informed.co/v1/feed';

// Create a simple test CSV
const testCsv = `sku,marketplace_id,cost,currency,min_price,max_price,map_price,listing_type,strategy_id,managed,dateadded
TEST-SKU-001,9432,10.00,USD,15.00,50.00,20.00,Amazon FBA,1,1,2024-01-01
TEST-SKU-002,9432,12.00,USD,18.00,55.00,25.00,Amazon FBM,1,1,2024-01-01`;

async function testUpload() {
    console.log('Testing CSV upload to Informed API...\n');
    
    // Test 1: Save CSV to file and upload as file
    console.log('=== Test 1: Upload as actual file ===');
    try {
        fs.writeFileSync('test_upload.csv', testCsv);
        
        const formData1 = new FormData();
        formData1.append('file', fs.createReadStream('test_upload.csv'), {
            filename: 'test_upload.csv',
            contentType: 'text/csv'
        });
        
        const response1 = await fetch(UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData1.getHeaders()
            },
            body: formData1
        });
        
        console.log(`Status: ${response1.status} ${response1.statusText}`);
        const text1 = await response1.text();
        console.log(`Response: ${text1}\n`);
        
        // Clean up
        fs.unlinkSync('test_upload.csv');
        
    } catch (error) {
        console.log(`Error: ${error.message}\n`);
    }
    
    // Test 2: Upload as Buffer
    console.log('=== Test 2: Upload as Buffer ===');
    try {
        const formData2 = new FormData();
        formData2.append('file', Buffer.from(testCsv, 'utf8'), {
            filename: 'test_upload.csv',
            contentType: 'text/csv'
        });
        
        const response2 = await fetch(UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData2.getHeaders()
            },
            body: formData2
        });
        
        console.log(`Status: ${response2.status} ${response2.statusText}`);
        const text2 = await response2.text();
        console.log(`Response: ${text2}\n`);
        
    } catch (error) {
        console.log(`Error: ${error.message}\n`);
    }
    
    // Test 3: Upload with different content type
    console.log('=== Test 3: Upload with text/tab-separated-values ===');
    try {
        const formData3 = new FormData();
        formData3.append('file', Buffer.from(testCsv, 'utf8'), {
            filename: 'test_upload.csv',
            contentType: 'text/tab-separated-values'
        });
        
        const response3 = await fetch(UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData3.getHeaders()
            },
            body: formData3
        });
        
        console.log(`Status: ${response3.status} ${response3.statusText}`);
        const text3 = await response3.text();
        console.log(`Response: ${text3}\n`);
        
    } catch (error) {
        console.log(`Error: ${error.message}\n`);
    }
    
    // Test 4: Upload with application/octet-stream
    console.log('=== Test 4: Upload with application/octet-stream ===');
    try {
        const formData4 = new FormData();
        formData4.append('file', Buffer.from(testCsv, 'utf8'), {
            filename: 'test_upload.csv',
            contentType: 'application/octet-stream'
        });
        
        const response4 = await fetch(UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                ...formData4.getHeaders()
            },
            body: formData4
        });
        
        console.log(`Status: ${response4.status} ${response4.statusText}`);
        const text4 = await response4.text();
        console.log(`Response: ${text4}\n`);
        
    } catch (error) {
        console.log(`Error: ${error.message}\n`);
    }
    
    // Test 5: Check what headers are being sent
    console.log('=== Test 5: Debug headers ===');
    const formData5 = new FormData();
    formData5.append('file', Buffer.from(testCsv, 'utf8'), {
        filename: 'test_upload.csv',
        contentType: 'text/csv'
    });
    
    const headers = formData5.getHeaders();
    console.log('FormData headers:', headers);
    console.log('Content-Type:', headers['content-type']);
}

testUpload().catch(console.error);
