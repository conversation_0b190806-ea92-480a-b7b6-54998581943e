// testGoogleSheetsImport.js - Test script for Google Sheets import functionality

import { importDiscsFromGoogleSheets } from './googleSheetsImporter.js';

// Test with a sample URL (you'll need to replace with your actual public URL)
const testUrl = 'https://docs.google.com/spreadsheets/d/1POLzlD73Es0pvsiBYEIORNcKHALVWRFyzg9ohnjfyg8/edit?gid=0#gid=0';

console.log('Testing Google Sheets import...');
console.log('URL:', testUrl);

// Test validation only first
console.log('\n=== Testing Validation Only ===');
try {
    const validationResult = await importDiscsFromGoogleSheets(testUrl, true);
    console.log('Validation Result:', JSON.stringify(validationResult, null, 2));
} catch (error) {
    console.error('Validation Error:', error.message);
    console.log('\nTo make your Google Sheet accessible:');
    console.log('1. Open your Google Sheet');
    console.log('2. Click "Share" button in the top right');
    console.log('3. Click "Change to anyone with the link"');
    console.log('4. Set permission to "Viewer"');
    console.log('5. Click "Done"');
    console.log('6. The sheet should now be publicly accessible for import');
}

// If validation passes, you can uncomment this to test actual import
/*
console.log('\n=== Testing Actual Import ===');
try {
    const importResult = await importDiscsFromGoogleSheets(testUrl, false);
    console.log('Import Result:', JSON.stringify(importResult, null, 2));
} catch (error) {
    console.error('Import Error:', error.message);
}
*/
