import { exportOnly } from './informedUploadHandler.js';

async function testUpdatedCSV() {
    try {
        console.log('Testing updated CSV function...');
        const result = await exportOnly();
        
        if (result.success) {
            const lines = result.csvContent.split('\n');
            console.log('=== UPDATED CSV HEADER ===');
            console.log(lines[0]);
            console.log('=== UPDATED FIRST DATA ROW ===');
            console.log(lines[1]);
            console.log('=== FIRST 200 CHARS ===');
            console.log(result.csvContent.substring(0, 200));
            
            // Show the difference
            console.log('=== COMPARISON ===');
            console.log('Header has quotes?', lines[0].includes('"'));
            console.log('Data row has quotes?', lines[1].includes('"'));
            
            // Show what the JSON payload would look like
            const requestBody = {
                data: result.csvContent,
                format: 'csv'
            };
            
            console.log('=== JSON PAYLOAD PREVIEW ===');
            const jsonString = JSON.stringify(requestBody);
            console.log('First 300 chars of JSON payload:');
            console.log(jsonString.substring(0, 300));
            
        } else {
            console.error('Export failed:', result.error);
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

testUpdatedCSV();
