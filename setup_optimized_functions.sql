-- Functions for the optimized Informed system
-- Run this AFTER running setup_optimized_informed_system.sql

-- Step 6: <PERSON>reate functions to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_informed_materialized_views()
R<PERSON><PERSON>NS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW mv_config_cache;
    REFRESH MATERIALIZED VIEW mv_sdasin_disc_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Create function to refresh just the config cache
CREATE OR REPLACE FUNCTION refresh_config_cache()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW mv_config_cache;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Create function to get count from optimized view
CREATE OR REPLACE FUNCTION fn_count_v_informed_upload_optimized()
RETURNS integer AS $$
DECLARE
    record_count integer;
BEGIN
    SELECT COUNT(*) INTO record_count FROM v_informed_upload_optimized;
    RETURN record_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN -1; -- Return -1 to indicate error
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 9: Create function to fill tu_informed using the optimized view
CREATE OR REPLACE FUNCTION public.fn_fill_tu_informed_optimized()
RETURNS TABLE(
    success boolean,
    record_count integer,
    error_message text
) AS $$
DECLARE
    inserted_count integer;
BEGIN
    -- First refresh the materialized views to ensure fresh data
    PERFORM refresh_informed_materialized_views();
    
    -- Insert data from optimized view into tu_informed
    INSERT INTO tu_informed (
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        map_price,
        listing_type,
        strategy_id,
        managed,
        dateadded
    )
    SELECT 
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        COALESCE(map_price, 0) AS map_price,  -- Replace NULL with 0
        listing_type,
        strategy_id,
        managed,
        dateadded
    FROM v_informed_upload_optimized;
    
    -- Get the count of inserted records
    GET DIAGNOSTICS inserted_count = ROW_COUNT;
    
    -- Return success result
    RETURN QUERY SELECT true, inserted_count, null::text;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error result
        RETURN QUERY SELECT false, 0, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 10: Initial refresh of materialized views
SELECT refresh_informed_materialized_views();

-- Step 11: Test the setup
SELECT 'Setup completed successfully!' as status;
SELECT fn_count_v_informed_upload_optimized() as optimized_record_count;
