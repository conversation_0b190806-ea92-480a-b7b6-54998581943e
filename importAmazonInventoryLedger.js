// importAmazonInventoryLedger.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Supabase credentials not found in .env file');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Map file headers to database column names
function mapHeaderToDbColumn(header) {
  const mapping = {
    'Date': 'Date',
    'FNSKU': 'FNSKU',
    'ASIN': 'ASIN',
    'MSKU': 'MSKU',
    'Title': 'Title',
    'Disposition': 'Disposition',
    'Starting Warehouse Balance': 'Starting_Warehouse_Balance',
    'In Transit Between Warehouses': 'In_Transit_Between_Warehouses',
    'Receipts': 'Receipts',
    'Customer Shipments': 'Customer_Shipments',
    'Customer Returns': 'Customer_Returns',
    'Vendor Returns': 'Vendor_Returns',
    'Warehouse Transfer In/Out': 'Warehouse_Transfer_In_Out',
    'Found': 'Found',
    'Lost': 'Lost',
    'Damaged': 'Damaged',
    'Disposed': 'Disposed',
    'Other Events': 'Other_Events',
    'Ending Warehouse Balance': 'Ending_Warehouse_Balance',
    'Unknown Events': 'Unknown_Events',
    'Location': 'Location',
    'Store': 'Store'
  };

  return mapping[header] || header;
}

// Generate a unique batch ID based on filename and file content hash
function generateBatchId(fileName, fileContent) {
  const hash = crypto.createHash('md5').update(`${fileName}_${fileContent}`).digest('hex');
  // Convert MD5 hash to UUID format (8-4-4-4-12)
  return `${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}`;
}

// Check if a batch has already been imported
async function checkIfBatchExists(batchId) {
  const { data, error } = await supabase
    .from('it_amaz_monthly_inventory_ledger_summary')
    .select('import_batch_id')
    .eq('import_batch_id', batchId)
    .limit(1);

  if (error) {
    console.error(`[importAmazonInventoryLedger] Error checking batch existence: ${error.message}`);
    return false;
  }

  return data && data.length > 0;
}

export async function importAmazonInventoryLedger() {
  const dataDir = path.join(__dirname, 'data', 'external data');

  console.log(`[importAmazonInventoryLedger] Scanning directory: ${dataDir}`);

  // Ensure the data directory exists
  if (!fs.existsSync(dataDir)) {
    throw new Error(`Data directory does not exist: ${dataDir}`);
  }

  // Find all Amazon Inventory Ledger Summary files
  const files = fs.readdirSync(dataDir);
  const ledgerFiles = files.filter(file =>
    file.match(/^Amazon Inventory Ledger Summary \d{4}-\d{2}\.txt$/i)
  );

  if (ledgerFiles.length === 0) {
    throw new Error(`No Amazon Inventory Ledger Summary files found in ${dataDir}`);
  }

  console.log(`[importAmazonInventoryLedger] Found ${ledgerFiles.length} Amazon Inventory Ledger Summary files`);

  let totalImported = 0;
  const processedFiles = [];

  // Process each file
  for (const fileName of ledgerFiles) {
    console.log(`[importAmazonInventoryLedger] Processing ${fileName}...`);

    const filePath = path.join(dataDir, fileName);

    // Extract date from filename (format: "Amazon Inventory Ledger Summary YYYY-MM.txt")
    const dateMatch = fileName.match(/Amazon Inventory Ledger Summary (\d{4}-\d{2})\.txt/i);
    const reportDate = dateMatch ? dateMatch[1] : null;

    if (!reportDate) {
      console.warn(`[importAmazonInventoryLedger] Could not extract date from filename: ${fileName}`);
      continue;
    }

    console.log(`[importAmazonInventoryLedger] Report date: ${reportDate}`);

    try {
      // Read and parse the file
      const fileContent = fs.readFileSync(filePath, 'utf-8');

      // Generate batch ID for this file
      const batchId = generateBatchId(fileName, fileContent);
      console.log(`[importAmazonInventoryLedger] Generated batch ID: ${batchId}`);

      // Check if this batch has already been imported
      const batchExists = await checkIfBatchExists(batchId);
      if (batchExists) {
        console.log(`[importAmazonInventoryLedger] Batch ${batchId} already exists, skipping import of ${fileName}`);
        processedFiles.push({
          fileName,
          reportDate,
          recordsImported: 0,
          skipped: true,
          reason: 'Already imported'
        });
        continue;
      }

      const lines = fileContent.split('\n');

      if (lines.length < 2) {
        console.warn(`[importAmazonInventoryLedger] File ${fileName} appears to be empty or has no data rows`);
        continue;
      }

      // Parse header line to get column names
      const headerLine = lines[0].trim();
      const headers = headerLine.split('\t').map(h => h.replace(/"/g, ''));

      console.log(`[importAmazonInventoryLedger] Found ${headers.length} columns: ${headers.slice(0, 5).join(', ')}...`);

      // Parse data lines
      const dataRows = [];
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue; // Skip empty lines

        const values = line.split('\t').map(v => v.replace(/"/g, ''));

        // Handle missing values by padding with empty strings
        while (values.length < headers.length) {
          values.push('');
        }

        // If there are too many values, truncate (shouldn't happen but just in case)
        if (values.length > headers.length) {
          console.warn(`[importAmazonInventoryLedger] Row ${i} has ${values.length} values but expected ${headers.length}, truncating extra values`);
          values.splice(headers.length);
        }

        // Create row object with mapped column names
        const row = {};
        headers.forEach((header, index) => {
          // Map file headers to database column names
          const dbColumnName = mapHeaderToDbColumn(header);
          row[dbColumnName] = values[index] || null;
        });

        // Add batch ID for idempotency
        row.import_batch_id = batchId;

        dataRows.push(row);
      }

      console.log(`[importAmazonInventoryLedger] Parsed ${dataRows.length} data rows from ${fileName}`);

      if (dataRows.length === 0) {
        console.warn(`[importAmazonInventoryLedger] No valid data rows found in ${fileName}`);
        continue;
      }

      // Insert data into Supabase in batches
      const batchSize = 1000;
      let importedFromFile = 0;

      for (let i = 0; i < dataRows.length; i += batchSize) {
        const batch = dataRows.slice(i, i + batchSize);

        console.log(`[importAmazonInventoryLedger] Inserting batch ${Math.floor(i/batchSize) + 1} (${batch.length} records)...`);

        const { data, error } = await supabase
          .from('it_amaz_monthly_inventory_ledger_summary')
          .insert(batch);

        if (error) {
          console.error(`[importAmazonInventoryLedger] Error inserting batch: ${error.message}`);
          throw error;
        }

        importedFromFile += batch.length;
      }

      console.log(`[importAmazonInventoryLedger] Imported ${importedFromFile} records from ${fileName}`);
      totalImported += importedFromFile;

      processedFiles.push({
        fileName,
        reportDate,
        recordsImported: importedFromFile,
        skipped: false,
        batchId: batchId
      });

    } catch (error) {
      console.error(`[importAmazonInventoryLedger] Error processing ${fileName}: ${error.message}`);
      throw error;
    }
  }

  // Log the import in the task queue
  try {
    const { error: logError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'amazon_fba_import_completed',
        payload: {
          fba_inventory_count: totalImported,
          processed_files: processedFiles,
          truncated: false
        },
        status: 'completed',
        result: 'Imported Amazon FBA inventory data via Node.js script',
        scheduled_at: new Date().toISOString(),
        processed_at: new Date().toISOString(),
        enqueued_by: 'importAmazonInventoryLedger.js'
      });

    if (logError) {
      console.warn(`[importAmazonInventoryLedger] Could not log import: ${logError.message}`);
    }
  } catch (logError) {
    console.warn(`[importAmazonInventoryLedger] Could not log import: ${logError.message}`);
  }

  const importedFiles = processedFiles.filter(f => !f.skipped);
  const skippedFiles = processedFiles.filter(f => f.skipped);

  const message = `Imported ${totalImported} records from ${importedFiles.length} files into it_amaz_monthly_inventory_ledger_summary${skippedFiles.length > 0 ? ` (${skippedFiles.length} files skipped - already imported)` : ''}`;

  const details = processedFiles.map(f => {
    if (f.skipped) {
      return `${f.fileName}: SKIPPED - ${f.reason} (${f.reportDate})`;
    } else {
      return `${f.fileName}: ${f.recordsImported} records imported (${f.reportDate}) [Batch: ${f.batchId}]`;
    }
  }).join('\n');

  console.log(`[importAmazonInventoryLedger] ${message}`);

  return {
    success: true,
    message,
    details,
    importCount: totalImported,
    processedFiles
  };
}

// Allow running this script directly
if (import.meta.url === `file://${process.argv[1]}`) {
  importAmazonInventoryLedger()
    .then(result => {
      console.log('Import completed successfully!');
      console.log(result.message);
    })
    .catch(error => {
      console.error('Import failed:', error.message);
      process.exit(1);
    });
}
