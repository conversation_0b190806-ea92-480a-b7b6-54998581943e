import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStrategyConfig() {
    try {
        console.log('Checking strategy ID configuration...');

        // Check what strategy IDs are configured in v_sdasins
        console.log('\n=== STRATEGY IDs IN v_sdasins ===');
        const { data: strategyData, error: strategyError } = await supabase
            .from('v_sdasins')
            .select('id, fba_informed_strategy_id, fbm_informed_strategy_id')
            .limit(10);

        if (strategyError) {
            console.error('Error fetching strategy data:', strategyError);
        } else {
            console.log('Sample strategy IDs from v_sdasins:');
            strategyData.forEach(row => {
                console.log(`SDASIN ${row.id}: FBA=${row.fba_informed_strategy_id}, FBM=${row.fbm_informed_strategy_id}`);
            });
        }

        // Check unique strategy IDs
        console.log('\n=== UNIQUE STRATEGY IDs ===');
        const { data: uniqueStrategies, error: uniqueError } = await supabase.rpc('exec_sql', {
            sql_query: `
                SELECT DISTINCT fba_informed_strategy_id as strategy_id, 'FBA' as type
                FROM v_sdasins
                WHERE fba_informed_strategy_id IS NOT NULL
                UNION
                SELECT DISTINCT fbm_informed_strategy_id as strategy_id, 'FBM' as type
                FROM v_sdasins
                WHERE fbm_informed_strategy_id IS NOT NULL
                ORDER BY strategy_id;
            `
        });

        if (uniqueError) {
            console.error('Error fetching unique strategies:', uniqueError);
        } else {
            console.log('Unique strategy IDs configured:');
            uniqueStrategies.forEach(row => {
                console.log(`Strategy ID ${row.strategy_id} (${row.type})`);
            });
        }

        // Check current view being used
        console.log('\n=== CURRENT VIEW BEING USED ===');
        const { data: currentView, error: viewError } = await supabase.rpc('exec_sql', {
            sql_query: `
                SELECT strategy_id, COUNT(*) as count
                FROM v_informed_upload_minimal
                GROUP BY strategy_id
                ORDER BY strategy_id;
            `
        });

        if (viewError) {
            console.error('Error checking current view:', viewError);
        } else {
            console.log('Strategy IDs in current v_informed_upload_minimal:');
            currentView.forEach(row => {
                console.log(`Strategy ID ${row.strategy_id}: ${row.count} records`);
            });
        }

    } catch (error) {
        console.error('Error:', error);
    }
}

checkStrategyConfig();
