// Test the upload endpoint directly
async function testUploadEndpoint() {
    try {
        console.log('Testing /api/informed/upload-csv endpoint...');
        
        const response = await fetch('http://localhost:3001/api/informed/upload-csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                csvContent: 'SKU,MARKETPLACE_ID,COST,CURRENCY,MIN_PRICE,MAX_PRICE,MANAGED,MAP_PRICE,LISTING_TYPE,STRATEGY_ID,DateAdded\ntest_sku,9432,10.00,USD,15.00,50.00,1,20.00,Amazon FBA,37123,1/1/2025 12:00:00'
            })
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('Response text:', responseText);
        
        if (response.headers.get('content-type')?.includes('application/json')) {
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed JSON:', data);
            } catch (e) {
                console.log('Failed to parse as JSON:', e.message);
            }
        }
        
    } catch (error) {
        console.error('Error testing endpoint:', error);
    }
}

testUploadEndpoint();
