<#
.SYNOPSIS
    Imports Amazon Inventory Ledger Summary reports into PostgreSQL database
.DESCRIPTION
    Processes .txt files with naming pattern "Amazon Inventory Ledger Summary YYYY-MM.txt"
    from the specified directory and imports them into the it_amaz_monthly_inventory_ledger_summary table
.PARAMETER dataDir
    Directory containing the Amazon Inventory Ledger Summary .txt files
#>

param (
    [string]$dataDir = "C:\Users\<USER>\supabase_project\data\external data"
)

# Load environment variables from .env file
$envFile = Join-Path $PSScriptRoot ".env"
if (Test-Path $envFile) {
    Get-Content $envFile | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]+)=(.*)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            # Remove quotes if present
            if ($value -match '^"(.*)"$' -or $value -match "^'(.*)'$") {
                $value = $matches[1]
            }
            Set-Item -Path "env:$key" -Value $value
        }
    }
    Write-Host "Loaded environment variables from .env file"
} else {
    Write-Error ".env file not found at $envFile"
    exit 1
}

# Get Supabase credentials from environment variables
$supabaseUrl = $env:SUPABASE_URL
$supabaseKey = $env:SUPABASE_KEY

if (-not $supabaseUrl -or -not $supabaseKey) {
    Write-Error "Supabase credentials not found in .env file"
    exit 1
}

# Ensure the data directory exists
if (-not (Test-Path $dataDir)) {
    Write-Error "Data directory does not exist: $dataDir"
    exit 1
}

# Find all Amazon Inventory Ledger Summary files
$ledgerFiles = Get-ChildItem -Path $dataDir -Filter "Amazon Inventory Ledger Summary *.txt"

if ($ledgerFiles.Count -eq 0) {
    Write-Error "No Amazon Inventory Ledger Summary files found in $dataDir"
    exit 1
}

Write-Host "Found $($ledgerFiles.Count) Amazon Inventory Ledger Summary files"

# Create a temporary directory for processing
$tempDir = Join-Path $env:TEMP "amazon_ledger_import_$(Get-Date -Format 'yyyyMMddHHmmss')"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Total records imported counter
$totalImported = 0

# Process each file
foreach ($file in $ledgerFiles) {
    Write-Host "Processing $($file.Name)..."
    
    # Extract date from filename (format: "Amazon Inventory Ledger Summary YYYY-MM.txt")
    if ($file.Name -match "Amazon Inventory Ledger Summary (\d{4}-\d{2})\.txt") {
        $reportDate = $matches[1]
        Write-Host "Report date: $reportDate"
    } else {
        Write-Warning "Could not extract date from filename: $($file.Name). Using file creation date instead."
        $reportDate = $file.CreationTime.ToString("yyyy-MM")
    }
    
    # Create import SQL script
    $importScript = @"
-- Import data from $($file.Name)
\copy public.it_amaz_monthly_inventory_ledger_summary ("Date", "FNSKU", "ASIN", "MSKU", "Title", "Disposition", "Starting_Warehouse_Balance", "In_Transit_Between_Warehouses", "Receipts", "Customer_Shipments", "Customer_Returns", "Vendor_Returns", "Warehouse_Transfer_In_Out", "Found", "Lost", "Damaged", "Disposed", "Other_Events", "Ending_Warehouse_Balance", "Unknown_Events", "Location", "Store") FROM '$($file.FullName)' WITH (FORMAT csv, DELIMITER E'\t', HEADER true, QUOTE '"');
"@
    
    $importFile = Join-Path $tempDir "import_$($file.BaseName).sql"
    Set-Content -Path $importFile -Value $importScript
    
    # Run the import using psql with credentials from .env
    Write-Host "Importing $($file.Name)..."
    $importResult = & psql -q -f $importFile -d $env:PGDATABASE -h $env:PGHOST -p $env:PGPORT -U $env:PGUSER 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to import $($file.Name): $importResult"
        continue
    }
    
    # Count imported records
    $countScript = @"
SELECT COUNT(*) FROM public.it_amaz_monthly_inventory_ledger_summary WHERE "Date" = '$reportDate';
"@
    
    $countFile = Join-Path $tempDir "count_$($file.BaseName).sql"
    Set-Content -Path $countFile -Value $countScript
    
    $countResult = & psql -t -q -f $countFile -d $env:PGDATABASE -h $env:PGHOST -p $env:PGPORT -U $env:PGUSER 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        $importedCount = [int]($countResult.Trim())
        $totalImported += $importedCount
        Write-Host "Imported $importedCount records from $($file.Name)"
    } else {
        Write-Warning "Could not count imported records: $countResult"
    }
}

# Log the import
$finalScript = @"
-- Log the import
INSERT INTO public.t_task_queue (
  task_type,
  payload,
  status,
  result,
  scheduled_at,
  created_at,
  completed_at,
  enqueued_by
) VALUES (
  'amazon_fba_import_completed',
  jsonb_build_object(
    'fba_inventory_count', $totalImported,
    'total_records', (SELECT COUNT(*) FROM public.it_amaz_monthly_inventory_ledger_summary),
    'truncated', false
  ),
  'completed',
  'Imported Amazon FBA inventory data via PowerShell script',
  NOW(),
  NOW(),
  NOW(),
  'import_amazon_ledger.ps1'
);

-- Print the results
SELECT 'Imported ' || $totalImported || ' records into it_amaz_monthly_inventory_ledger_summary (Total records: ' || COUNT(*) || ')' FROM public.it_amaz_monthly_inventory_ledger_summary;
"@

$finalFile = Join-Path $tempDir "final.sql"
Set-Content -Path $finalFile -Value $finalScript

$finalResult = & psql -t -q -f $finalFile -d $env:PGDATABASE -h $env:PGHOST -p $env:PGPORT -U $env:PGUSER 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host $finalResult
} else {
    Write-Warning "Could not log import: $finalResult"
}

# Clean up
Remove-Item -Path $tempDir -Recurse -Force

Write-Host "Import completed successfully!"