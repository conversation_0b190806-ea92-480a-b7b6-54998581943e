-- Drop and recreate the minimal view with correct data types

-- First, drop the existing view
DROP VIEW IF EXISTS v_informed_upload_minimal;

-- Recreate with correct data types
CREATE VIEW v_informed_upload_minimal AS
SELECT 
    ts.fba_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Hardcoded for now
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Hardcoded for now
    50.00::numeric as max_price,  -- Hardcoded for now
    20.00::numeric as map_price,  -- Hardcoded for now
    'Amazon FBA'::text as listing_type,
    1::bigint as strategy_id,  -- Use bigint to match tu_informed table
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
WHERE ts.fba_uploaded_at IS NOT NULL
  AND ts.fba_sku IS NOT NULL

UNION ALL

SELECT 
    ts.fbm_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Hardcoded for now
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Hardcoded for now
    50.00::numeric as max_price,  -- Hardcoded for now
    20.00::numeric as map_price,  -- Hardcoded for now
    'Amazon FBM'::text as listing_type,
    1::bigint as strategy_id,  -- Use bigint to match tu_informed table
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
WHERE ts.fbm_uploaded_at IS NOT NULL
  AND ts.fbm_sku IS NOT NULL;

-- Test the recreated view
SELECT COUNT(*) as total_records FROM v_informed_upload_minimal;

-- Show a sample to verify data types
SELECT * FROM v_informed_upload_minimal LIMIT 3;

-- Verify the column types
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'v_informed_upload_minimal' 
  AND column_name = 'strategy_id';
