-- Step 4: Create functions for the minimal approach

-- Function to count records in the minimal view
CREATE OR REPLACE FUNCTION fn_count_v_informed_upload_minimal()
RETURNS integer AS $$
DECLARE
    record_count integer;
BEGIN
    SELECT COUNT(*) INTO record_count FROM v_informed_upload_minimal;
    RETURN record_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN -1; -- Return -1 to indicate error
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to fill tu_informed using the minimal view
CREATE OR REPLACE FUNCTION fn_fill_tu_informed_minimal()
RETURNS TABLE(
    success boolean,
    record_count integer,
    error_message text
) AS $$
DECLARE
    inserted_count integer;
BEGIN
    -- Insert data from minimal view into tu_informed
    INSERT INTO tu_informed (
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        map_price,
        listing_type,
        strategy_id,
        managed,
        dateadded
    )
    SELECT 
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        COALESCE(map_price, 0) AS map_price,  -- Replace NULL with 0
        listing_type,
        strategy_id::text,  -- Convert to text
        managed,
        dateadded
    FROM v_informed_upload_minimal;
    
    -- Get the count of inserted records
    GET DIAGNOSTICS inserted_count = ROW_COUNT;
    
    -- Return success result
    RETURN QUERY SELECT true, inserted_count, null::text;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error result
        RETURN QUERY SELECT false, 0, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the functions
SELECT fn_count_v_informed_upload_minimal() as record_count;
