-- Step 5: Gradually add real data back to the minimal view
-- Run these one at a time to see which part causes slowdowns

-- Step 5a: Add real SKUs and strategy IDs (should be fast)
CREATE OR REPLACE VIEW v_informed_upload_with_real_skus AS
SELECT 
    ts.fba_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Still hardcoded
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Still hardcoded
    50.00::numeric as max_price,  -- Still hardcoded
    20.00::numeric as map_price,  -- Still hardcoded
    'Amazon FBA'::text as listing_type,
    COALESCE(vs.fba_informed_strategy_id, 1) as strategy_id,  -- Real strategy ID
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
LEFT JOIN v_sdasins vs ON ts.id = vs.id  -- Add this join back
WHERE ts.fba_uploaded_at IS NOT NULL
  AND ts.fba_sku IS NOT NULL

UNION ALL

SELECT 
    ts.fbm_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Still hardcoded
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Still hardcoded
    50.00::numeric as max_price,  -- Still hardcoded
    20.00::numeric as map_price,  -- Still hardcoded
    'Amazon FBM'::text as listing_type,
    COALESCE(vs.fbm_informed_strategy_id, 1) as strategy_id,  -- Real strategy ID
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
LEFT JOIN v_sdasins vs ON ts.id = vs.id  -- Add this join back
WHERE ts.fbm_uploaded_at IS NOT NULL
  AND ts.fbm_sku IS NOT NULL;

-- Test this version (should still be fast)
SELECT COUNT(*) as total_records FROM v_informed_upload_with_real_skus;

-- Step 5b: Add MPS and plastics data (might be slower)
CREATE OR REPLACE VIEW v_informed_upload_with_mps_data AS
SELECT 
    ts.fba_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Still hardcoded
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Still hardcoded
    -- Add real max price from MPS/plastics
    COALESCE(
        ts.override_amazon_max_price,
        tm.val_override_max_amazon_price,
        tp.val_max_amazon_price,
        tp.val_msrp,
        50.00  -- Fallback
    ) as max_price,
    -- Add real MAP price
    COALESCE(tm.val_override_map_price, tp.val_map_price, 20.00) as map_price,
    'Amazon FBA'::text as listing_type,
    COALESCE(vs.fba_informed_strategy_id, 1) as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
JOIN t_mps tm ON ts.mps_id = tm.id  -- Add MPS join
JOIN t_plastics tp ON tm.plastic_id = tp.id  -- Add plastics join
LEFT JOIN v_sdasins vs ON ts.id = vs.id
WHERE ts.fba_uploaded_at IS NOT NULL
  AND ts.fba_sku IS NOT NULL

UNION ALL

SELECT 
    ts.fbm_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Still hardcoded
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Still hardcoded
    -- Add real max price from MPS/plastics
    COALESCE(
        ts.override_amazon_max_price,
        tm.val_override_max_amazon_price,
        tp.val_max_amazon_price,
        tp.val_msrp,
        50.00  -- Fallback
    ) as max_price,
    -- Add real MAP price
    COALESCE(tm.val_override_map_price, tp.val_map_price, 20.00) as map_price,
    'Amazon FBM'::text as listing_type,
    COALESCE(vs.fbm_informed_strategy_id, 1) as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
JOIN t_mps tm ON ts.mps_id = tm.id  -- Add MPS join
JOIN t_plastics tp ON tm.plastic_id = tp.id  -- Add plastics join
LEFT JOIN v_sdasins vs ON ts.id = vs.id
WHERE ts.fbm_uploaded_at IS NOT NULL
  AND ts.fbm_sku IS NOT NULL;

-- Test this version (might be slower due to joins)
SELECT COUNT(*) as total_records FROM v_informed_upload_with_mps_data;
