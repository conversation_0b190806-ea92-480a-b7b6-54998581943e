# Google Sheets Import for t_discs

This feature allows you to import disc data from a Google Sheets document directly into the `t_discs` table in Supabase with full validation and foreign key mapping.

## Setup Instructions

### 1. Make Your Google Sheet Public

For the import to work, your Google Sheet must be publicly accessible:

1. Open your Google Sheet: https://docs.google.com/spreadsheets/d/1POLzlD73Es0pvsiBYEIORNcKHALVWRFyzg9ohnjfyg8/edit?gid=0#gid=0
2. Click the "Share" button in the top right corner
3. Click "Change to anyone with the link"
4. Set the permission to "Viewer"
5. Click "Done"

### 2. Required Column Headers

Your Google Sheet must have the following column headers (case-sensitive):

- `shipment_id` (required) - Must exist in t_shipments.id
- `mps_id` (required) - Must exist in t_mps.id
- `color` (required) - Must match a value in t_colors.color
- `weight_scale` (optional) - Positive number, maps to t_discs.weight
- `weight_mfg` (optional) - Positive number, maps to t_discs.weight_mfg
- `grade` (optional) - Integer between -10 and 10
- `color_modifier` (optional) - Text
- `description` (optional) - Text
- `notes` (optional) - Text
- `location` (optional) - Text
- `new_id` (optional) - Will be populated with new t_discs.id values after import

### Column Mapping

The importer maps your Google Sheet columns to the t_discs table as follows:
- `weight_scale` → `weight` (scale weight becomes the main weight field)
- `weight_mfg` → `weight_mfg` (manufacturer weight stays as weight_mfg)

## How to Use

### Via Admin Interface

1. Start the admin server: `node adminServer.js`
2. Open http://localhost:3001/admin.html
3. Go to the "Tasks" tab
4. Find the "Import Discs from Google Sheets" section
5. Paste your Google Sheets URL
6. Check "Validate Only" to test first (recommended)
7. Click "Import Discs from Google Sheets"

### Via Command Line (Testing)

```bash
node testGoogleSheetsImport.js
```

## Validation Process

The system performs comprehensive validation before import:

### Required Field Validation
- `shipment_id` must be provided and exist in t_shipments
- `mps_id` must be provided and exist in t_mps
- `color` must be provided and match a color in t_colors

### Data Type Validation
- `weight` must be a positive number if provided
- `grade` must be an integer between -10 and 10 if provided
- `shipment_id` and `mps_id` must be valid integers

### Foreign Key Validation
- Checks that shipment_id exists in t_shipments.id
- Checks that mps_id exists in t_mps.id
- Maps color name to color_id from t_colors table

## Import Process

1. **Fetch Data**: Downloads CSV data from Google Sheets
2. **Load Lookup Data**: Loads reference data from t_shipments, t_mps, and t_colors
3. **Validate Records**: Validates each row and reports errors
4. **Transform Data**: Maps color names to color_id and prepares for database
5. **Batch Insert**: Inserts all valid records in a single transaction
6. **Return Results**: Provides new IDs to update back to the Google Sheet

## Error Handling

- Invalid records are skipped and reported with specific error messages
- Row numbers are provided for easy identification of issues
- Foreign key violations are clearly identified
- Data type errors include the expected format

## Sample Data Format

```csv
shipment_id,mps_id,color,weight_scale,weight_mfg,grade,color_modifier,description,notes,location,new_id
123,456,Blue,175.5,175,8,Swirl,Champion Destroyer,Great condition,A1,
124,457,Red,174.0,174,9,,Star Wraith,New disc,B2,
```

## API Endpoint

The feature adds a new API endpoint:

```
POST /api/import-discs-from-sheets
Content-Type: application/json

{
  "googleSheetsUrl": "https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit#gid=0",
  "validateOnly": false
}
```

## Files Added

- `googleSheetsImporter.js` - Main import logic
- `testGoogleSheetsImport.js` - Test script
- Updated `admin.html` - Added UI form
- Updated `adminServer.js` - Added API endpoint

## Troubleshooting

### "Failed to fetch Google Sheets data"
- Make sure the sheet is publicly accessible
- Verify the URL is correct
- Check that the sheet contains data

### "shipment_id X does not exist"
- Verify the shipment_id exists in your t_shipments table
- Check for typos in the shipment_id values

### "mps_id X does not exist"
- Verify the mps_id exists in your t_mps table
- Check for typos in the mps_id values

### "color 'X' does not exist"
- Verify the color name exactly matches a value in t_colors.color
- Check for extra spaces or different capitalization

## Security Notes

- The Google Sheet must be publicly accessible for this to work
- Consider using a dedicated import sheet rather than your main data sheet
- The import creates records with `created_by = 'google_sheets_import'`
- All database operations use your existing Supabase credentials
