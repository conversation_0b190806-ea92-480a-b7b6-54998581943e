/**
 * Informed Scheduler
 *
 * This script schedules the download and import of Informed reports at specified times.
 * It uses node<PERSON>cron to schedule the tasks and runs them at 6:30 AM, 12:30 PM, and 6:30 PM CST.
 */

import nodeCron from 'node-cron';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { downloadAllReports } from './informedReportDownloader.js';
import { importAllReports } from './informedDirectImporter.js';
import { runCompleteUploadWorkflow } from './informedUploadHandler.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
const CONFIG_FILE = path.join(__dirname, 'informedSchedulerConfig.json');
const DEFAULT_CONFIG = {
    enabled: false,
    schedule: '30 6,12,18 * * *', // 6:30 AM, 12:30 PM, 6:30 PM every day
    lastRun: null,
    nextRun: null
};

// Global variables
let scheduler = null;

/**
 * Load the scheduler configuration
 * @returns {Object} - Scheduler configuration
 */
function loadConfig() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
            return { ...DEFAULT_CONFIG, ...config };
        }
    } catch (error) {
        console.error('Error loading scheduler config:', error);
    }

    return DEFAULT_CONFIG;
}

/**
 * Save the scheduler configuration
 * @param {Object} config - Scheduler configuration to save
 */
function saveConfig(config) {
    try {
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    } catch (error) {
        console.error('Error saving scheduler config:', error);
    }
}

/**
 * Calculate the next run time based on the cron schedule
 * @param {string} schedule - Cron schedule expression
 * @returns {Date} - Next run time
 */
async function calculateNextRun(schedule) {
    try {
        // Dynamic import for cron-parser
        const cronParserModule = await import('cron-parser');
        const cronParser = cronParserModule.default || cronParserModule;
        const interval = cronParser.parseExpression(schedule);
        return interval.next().toDate();
    } catch (error) {
        console.error('Error calculating next run time:', error);
        return null;
    }
}

/**
 * Run the full Informed process (download, import, and upload)
 * @param {number} maxRetries - Maximum number of retries for checking status (default: 30)
 * @param {number} retryInterval - Interval between retries in milliseconds (default: 10000)
 * @returns {Promise<Object>} - Results of the process
 */
async function runFullProcess(maxRetries = 30, retryInterval = 10000) {
    console.log(`Running complete Informed process (download + import + upload) with maxRetries=${maxRetries}, retryInterval=${retryInterval}ms...`);

    try {
        const results = {
            download: null,
            import: null,
            upload: null
        };

        // Phase 1: Download reports (using same retry parameters as individual buttons)
        console.log('Phase 1: Downloading reports from Informed...');
        const downloadResults = await downloadAllReports(maxRetries, retryInterval);
        results.download = {
            success: downloadResults.every(r => r.success),
            reports: downloadResults
        };

        if (!results.download.success) {
            console.error('Download phase failed, skipping import and upload');
            return {
                success: false,
                error: 'Download phase failed',
                results
            };
        }

        // Phase 2: Import reports
        console.log('Phase 2: Importing reports to Supabase...');
        const importResults = await importAllReports();
        results.import = {
            success: importResults.every(r => r.success),
            reports: importResults
        };

        if (!results.import.success) {
            console.error('Import phase failed, skipping upload');
            return {
                success: false,
                error: 'Import phase failed',
                results
            };
        }

        // Phase 3: Upload pricing data
        console.log('Phase 3: Uploading pricing data to Informed...');
        const uploadResult = await runCompleteUploadWorkflow();
        results.upload = uploadResult;

        if (!uploadResult.success) {
            console.error('Upload phase failed');
            return {
                success: false,
                error: 'Upload phase failed',
                results
            };
        }

        // Update config with last run time
        const config = loadConfig();
        config.lastRun = new Date().toISOString();
        const nextRunDate = await calculateNextRun(config.schedule);
        config.nextRun = nextRunDate ? nextRunDate.toISOString() : null;
        saveConfig(config);

        console.log('Complete Informed process completed successfully!');
        return {
            success: true,
            message: 'Complete workflow (download + import + upload) completed successfully',
            results
        };
    } catch (error) {
        console.error('Error running complete Informed process:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Start the scheduler
 * @returns {Object} - Result of starting the scheduler
 */
function startScheduler() {
    const config = loadConfig();

    if (scheduler) {
        return {
            success: true,
            message: 'Scheduler is already running',
            nextRun: config.nextRun,
            schedule: config.schedule
        };
    }

    try {
        scheduler = nodeCron.schedule(config.schedule, async () => {
            console.log('Running scheduled Informed process...');
            await runFullProcess();
        });

        config.enabled = true;
        const nextRunDate = await calculateNextRun(config.schedule);
        config.nextRun = nextRunDate ? nextRunDate.toISOString() : null;
        saveConfig(config);

        console.log(`Scheduler started with schedule: ${config.schedule}`);
        console.log(`Next run: ${new Date(config.nextRun).toLocaleString()}`);

        return {
            success: true,
            message: 'Scheduler started successfully',
            nextRun: config.nextRun,
            schedule: config.schedule
        };
    } catch (error) {
        console.error('Error starting scheduler:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Stop the scheduler
 * @returns {Object} - Result of stopping the scheduler
 */
function stopScheduler() {
    if (!scheduler) {
        return {
            success: true,
            message: 'Scheduler is not running'
        };
    }

    try {
        scheduler.stop();
        scheduler = null;

        const config = loadConfig();
        config.enabled = false;
        saveConfig(config);

        console.log('Scheduler stopped');

        return {
            success: true,
            message: 'Scheduler stopped successfully'
        };
    } catch (error) {
        console.error('Error stopping scheduler:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get the current status of the scheduler
 * @returns {Object} - Scheduler status
 */
function getSchedulerStatus() {
    const config = loadConfig();

    return {
        enabled: config.enabled && scheduler !== null,
        schedule: config.schedule,
        lastRun: config.lastRun,
        nextRun: config.nextRun
    };
}

// Initialize the scheduler on module load if enabled in config
function initialize() {
    const config = loadConfig();

    if (config.enabled) {
        startScheduler();
    }
}

// Export functions for use in other modules
export {
    runFullProcess,
    startScheduler,
    stopScheduler,
    getSchedulerStatus,
    initialize
};

// If this script is run directly, initialize the scheduler
if (import.meta.url === `file://${process.argv[1]}`) {
    initialize();
}
