-- Step 6: Add carrying costs (the likely performance bottleneck)
-- This step adds back the v_sdasins_avg_carrying_costs join

-- Step 6a: Test the carrying costs view performance first
SELECT COUNT(*) as total_carrying_costs FROM v_sdasins_avg_carrying_costs;

-- Check if there are any obvious performance issues
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) FROM v_sdasins_avg_carrying_costs;

-- Step 6b: Create view with real carrying costs but simplified calculations
CREATE OR REPLACE VIEW v_informed_upload_with_carrying_costs AS
WITH config AS (
    SELECT * FROM mv_config_cache LIMIT 1
),
fba_data AS (
    SELECT 
        ts.fba_sku as sku,
        '9432'::text as marketplace_id,
        -- Add real carrying costs but keep calculations simple for now
        (GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fba, 0.0) + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85
        ) - 0.01)::numeric as cost,
        'USD'::text as currency,
        -- Calculate min price with real carrying costs
        GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fba, 0.0) + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85
        ) as min_price,
        -- Real max price from MPS/plastics
        COALESCE(
            ts.override_amazon_max_price,
            tm.val_override_max_amazon_price,
            tp.val_max_amazon_price,
            tp.val_msrp
        ) as max_price,
        -- Real MAP price
        COALESCE(tm.val_override_map_price, tp.val_map_price) as map_price,
        'Amazon FBA'::text as listing_type,
        COALESCE(vs.fba_informed_strategy_id, 1) as strategy_id,
        '1'::text as managed,
        now() as dateadded
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id  -- Add carrying costs join
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    CROSS JOIN config c
    WHERE ts.fba_uploaded_at IS NOT NULL
      AND ts.fba_sku IS NOT NULL
      AND vs.fba_informed_strategy_id IS NOT NULL
),
fbm_data AS (
    SELECT 
        ts.fbm_sku as sku,
        '9432'::text as marketplace_id,
        -- Add real carrying costs for FBM
        (GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fbm, 0.0) + c.fbm_fees) / 0.85
        ) - 0.01)::numeric as cost,
        'USD'::text as currency,
        -- Calculate min price with real carrying costs
        GREATEST(
            COALESCE(tm.val_override_map_price, tp.val_map_price, 0)::double precision,
            (COALESCE(vsa.avg_carrying_cost_fbm, 0.0) + c.fbm_fees) / 0.85
        ) as min_price,
        -- Real max price from MPS/plastics
        COALESCE(
            ts.override_amazon_max_price,
            tm.val_override_max_amazon_price,
            tp.val_max_amazon_price,
            tp.val_msrp
        ) as max_price,
        -- Real MAP price
        COALESCE(tm.val_override_map_price, tp.val_map_price) as map_price,
        'Amazon FBM'::text as listing_type,
        COALESCE(vs.fbm_informed_strategy_id, 1) as strategy_id,
        '1'::text as managed,
        now() as dateadded
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id  -- Add carrying costs join
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    CROSS JOIN config c
    WHERE ts.fbm_uploaded_at IS NOT NULL
      AND ts.fbm_sku IS NOT NULL
      AND vs.fbm_informed_strategy_id IS NOT NULL
)
SELECT * FROM fba_data
UNION ALL
SELECT * FROM fbm_data;

-- Test this version (this might be slower due to carrying costs join)
SELECT COUNT(*) as total_records FROM v_informed_upload_with_carrying_costs;

-- If the above is too slow, we can create an optimized version
-- Step 6c: Create function to use the new view
CREATE OR REPLACE FUNCTION fn_fill_tu_informed_with_carrying_costs()
RETURNS TABLE(
    success boolean,
    record_count integer,
    error_message text
) AS $$
DECLARE
    inserted_count integer;
BEGIN
    -- Refresh the config cache to ensure fresh data
    PERFORM refresh_config_cache();
    
    -- Insert data from view with carrying costs into tu_informed
    INSERT INTO tu_informed (
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        map_price,
        listing_type,
        strategy_id,
        managed,
        dateadded
    )
    SELECT 
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        COALESCE(map_price, 0) AS map_price,  -- Replace NULL with 0
        listing_type,
        strategy_id,
        managed,
        dateadded
    FROM v_informed_upload_with_carrying_costs;
    
    -- Get the count of inserted records
    GET DIAGNOSTICS inserted_count = ROW_COUNT;
    
    -- Return success result
    RETURN QUERY SELECT true, inserted_count, null::text;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error result
        RETURN QUERY SELECT false, 0, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the function
SELECT fn_fill_tu_informed_with_carrying_costs() as result;
