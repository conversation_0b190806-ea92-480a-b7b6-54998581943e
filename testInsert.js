// testInsert.js - Test inserting a task into the queue
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase key defined:', supabaseKey ? 'Yes' : 'No');

const supabase = createClient(supabaseUrl, supabaseKey);

async function testInsert() {
  try {
    console.log('Testing insert operation...');
    
    const now = new Date();
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'test_insert',
        status: 'complete',
        payload: JSON.stringify("Test Insert"),
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        processed_at: now.toISOString(),
        result: JSON.stringify(`Test insert at ${now.toISOString()}`)
      })
      .select();
    
    if (error) {
      console.error('Error inserting task:', error.message);
      console.error('Error details:', error);
      return;
    }
    
    if (!data || data.length === 0) {
      console.error('No data returned from insert operation');
      return;
    }
    
    console.log('Successfully inserted task with ID:', data[0].id);
    console.log('Task data:', data[0]);
  } catch (err) {
    console.error('Exception during insert:', err.message);
    console.error('Stack trace:', err.stack);
  }
}

// Run the function
testInsert()
  .then(() => {
    console.log('Test completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
