import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStrategySimple() {
    try {
        console.log('Checking strategy ID sources...');
        
        // Check unique strategy IDs in v_sdasins using simple queries
        console.log('\n=== FBA STRATEGY IDs IN v_sdasins ===');
        const { data: fbaStrategies, error: fbaError } = await supabase
            .from('v_sdasins')
            .select('fba_informed_strategy_id')
            .not('fba_informed_strategy_id', 'is', null)
            .limit(100);
            
        if (fbaError) {
            console.error('Error fetching FBA strategies:', fbaError);
        } else {
            const fbaUnique = [...new Set(fbaStrategies.map(r => r.fba_informed_strategy_id))];
            console.log('Unique FBA strategy IDs:', fbaUnique);
        }
        
        console.log('\n=== FBM STRATEGY IDs IN v_sdasins ===');
        const { data: fbmStrategies, error: fbmError } = await supabase
            .from('v_sdasins')
            .select('fbm_informed_strategy_id')
            .not('fbm_informed_strategy_id', 'is', null)
            .limit(100);
            
        if (fbmError) {
            console.error('Error fetching FBM strategies:', fbmError);
        } else {
            const fbmUnique = [...new Set(fbmStrategies.map(r => r.fbm_informed_strategy_id))];
            console.log('Unique FBM strategy IDs:', fbmUnique);
        }
        
        // Check current strategy distribution in tu_informed
        console.log('\n=== STRATEGY DISTRIBUTION IN tu_informed ===');
        const { data: tuStrategies, error: tuError } = await supabase
            .from('tu_informed')
            .select('strategy_id, listing_type')
            .limit(1000);
            
        if (tuError) {
            console.error('Error fetching tu_informed strategies:', tuError);
        } else {
            const strategyCount = {};
            tuStrategies.forEach(row => {
                const key = `${row.strategy_id} (${row.listing_type})`;
                strategyCount[key] = (strategyCount[key] || 0) + 1;
            });
            
            console.log('Strategy distribution in first 1000 tu_informed records:');
            Object.entries(strategyCount).forEach(([key, count]) => {
                console.log(`  ${key}: ${count} records`);
            });
        }
        
    } catch (error) {
        console.error('Error:', error);
    }
}

checkStrategySimple();
