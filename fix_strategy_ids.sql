-- Fix the strategy IDs in v_informed_upload_minimal to use real strategy IDs from v_sdasins

DROP VIEW IF EXISTS v_informed_upload_minimal;

CREATE VIEW v_informed_upload_minimal AS
SELECT 
    ts.fba_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Hardcoded for now
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Hardcoded for now
    50.00::numeric as max_price,  -- Hardcoded for now
    20.00::numeric as map_price,  -- Hardcoded for now
    'Amazon FBA'::text as listing_type,
    COALESCE(vs.fba_informed_strategy_id, 37123)::bigint as strategy_id,  -- Use real FBA strategy ID
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
LEFT JOIN v_sdasins vs ON ts.id = vs.id
WHERE ts.fba_uploaded_at IS NOT NULL
  AND ts.fba_sku IS NOT NULL

UNION ALL

SELECT 
    ts.fbm_sku as sku,
    '9432'::text as marketplace_id,
    10.00::numeric as cost,  -- Hardcoded for now
    'USD'::text as currency,
    15.00::numeric as min_price,  -- Hardcoded for now
    50.00::numeric as max_price,  -- Hardcoded for now
    20.00::numeric as map_price,  -- Hardcoded for now
    'Amazon FBM'::text as listing_type,
    COALESCE(vs.fbm_informed_strategy_id, 45364)::bigint as strategy_id,  -- Use real FBM strategy ID
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
LEFT JOIN v_sdasins vs ON ts.id = vs.id
WHERE ts.fbm_uploaded_at IS NOT NULL
  AND ts.fbm_sku IS NOT NULL;

-- Test the updated view
SELECT strategy_id, COUNT(*) as count
FROM v_informed_upload_minimal
GROUP BY strategy_id
ORDER BY strategy_id;
