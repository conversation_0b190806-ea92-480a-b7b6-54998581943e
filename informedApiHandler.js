/**
 * Informed API Handler
 *
 * This module provides API endpoints for the Informed process.
 * It is imported by adminServer.js to handle Informed-related requests.
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { importAllReports, importReportByType } from './informedDirectImporter.js';
import {
    runCompleteUploadWorkflow,
    truncateOnly,
    fillOnly,
    exportOnly,
    uploadOnly
} from './informedUploadHandler.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the Informed modules
let informedReportDownloader = null;
let informedReportImporter = null;
let informedScheduler = null;

// Initialize the Informed modules
async function initializeInformedModules() {
    try {
        // Use dynamic import for CommonJS modules
        const reportDownloaderModule = await import('./informedReportDownloader.cjs');
        const reportImporterModule = await import('./informedReportImporter.cjs');
        const schedulerModule = await import('./informedScheduler.cjs');

        informedReportDownloader = reportDownloaderModule.default || reportDownloaderModule;
        informedReportImporter = reportImporterModule.default || reportImporterModule;
        informedScheduler = schedulerModule.default || schedulerModule;

        // Initialize the scheduler
        informedScheduler.initialize();

        console.log('[informedApiHandler] Informed modules initialized successfully');
    } catch (error) {
        console.error('[informedApiHandler] Error initializing Informed modules:', error);
    }
}

// Initialize the modules when this file is imported
initializeInformedModules();

/**
 * Register Informed API endpoints with the Express app
 * @param {Object} app - Express app
 */
export function registerInformedApiEndpoints(app) {
    // API endpoint to get Informed report status
    app.get('/api/informed/status', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            const reports = await informedReportDownloader.getReportsStatus();

            res.json({
                success: true,
                reports
            });
        } catch (error) {
            console.error('[informedApiHandler] Error getting Informed report status:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to download Informed reports
    app.post('/api/informed/download-reports', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            // Get retry parameters from request or use defaults
            const maxRetries = req.body.maxRetries || 30;
            const retryInterval = req.body.retryInterval || 10000; // 10 seconds

            console.log(`[informedApiHandler] Downloading Informed reports (maxRetries: ${maxRetries}, retryInterval: ${retryInterval}ms)`);
            const results = await informedReportDownloader.downloadAllReports(maxRetries, retryInterval);

            res.json({
                success: true,
                reports: results
            });
        } catch (error) {
            console.error('[informedApiHandler] Error downloading Informed reports:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to import Informed reports
    app.post('/api/informed/import-reports', async (req, res) => {
        try {
            console.log('[informedApiHandler] Importing Informed reports using direct importer');

            // Use the direct importer to import the reports
            const results = await importAllReports();

            // Check if all imports were successful
            const allSuccessful = results.every(result => result.success);

            if (allSuccessful) {
                console.log('[informedApiHandler] All imports completed successfully');
                res.json({
                    success: true,
                    message: 'All imports completed successfully',
                    results
                });
            } else {
                const failedReports = results.filter(result => !result.success);
                console.error(`[informedApiHandler] ${failedReports.length} imports failed`);
                res.status(500).json({
                    success: false,
                    error: `${failedReports.length} imports failed`,
                    results
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error importing Informed reports:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error',
                details: error.stack || ''
            });
        }
    });

    // API endpoint to run the full Informed process
    app.post('/api/informed/run-full-process', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            // Get retry parameters from request or use defaults
            const maxRetries = req.body.maxRetries || 30;
            const retryInterval = req.body.retryInterval || 10000; // 10 seconds

            console.log(`[informedApiHandler] Running full Informed process (maxRetries: ${maxRetries}, retryInterval: ${retryInterval}ms)`);

            // Step 1: Download reports
            console.log('[informedApiHandler] Step 1: Downloading reports');
            const downloadResults = await informedReportDownloader.downloadAllReports(maxRetries, retryInterval);

            // Check if download was successful
            const downloadSuccessful = downloadResults.every(result => result.success);
            if (!downloadSuccessful) {
                const failedReports = downloadResults.filter(result => !result.success);
                console.error(`[informedApiHandler] Failed to download ${failedReports.length} reports`);
                return res.json({
                    success: false,
                    message: `Failed to download ${failedReports.length} reports`,
                    downloadResults,
                    importResults: null
                });
            }

            // Step 2: Import reports
            console.log('[informedApiHandler] Step 2: Importing reports');
            const importResults = await importAllReports();

            // Check if all imports were successful
            const importSuccessful = importResults.every(result => result.success);

            // Return combined results
            res.json({
                success: downloadSuccessful && importSuccessful,
                message: `Download: ${downloadSuccessful ? 'Success' : 'Failed'}, Import: ${importSuccessful ? 'Success' : 'Failed'}`,
                downloadResults,
                importResults
            });
        } catch (error) {
            console.error('[informedApiHandler] Error running full Informed process:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error',
                details: error.stack || ''
            });
        }
    });

    // API endpoint to get scheduler status
    app.get('/api/informed/scheduler-status', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            const status = informedScheduler.getSchedulerStatus();

            res.json({
                success: true,
                ...status
            });
        } catch (error) {
            console.error('[informedApiHandler] Error getting scheduler status:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to enable scheduler
    app.post('/api/informed/enable-scheduler', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            console.log('[informedApiHandler] Enabling Informed scheduler');
            const result = informedScheduler.startScheduler();

            res.json(result);
        } catch (error) {
            console.error('[informedApiHandler] Error enabling scheduler:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to disable scheduler
    app.post('/api/informed/disable-scheduler', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            console.log('[informedApiHandler] Disabling Informed scheduler');
            const result = informedScheduler.stopScheduler();

            res.json(result);
        } catch (error) {
            console.error('[informedApiHandler] Error disabling scheduler:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to truncate tu_informed table
    app.post('/api/informed/truncate-tu-informed', async (req, res) => {
        try {
            console.log('[informedApiHandler] Truncating tu_informed table');

            const result = await truncateOnly();

            if (result.success) {
                console.log('[informedApiHandler] tu_informed truncated successfully');
                res.json({
                    success: true,
                    message: 'tu_informed truncated successfully',
                    deletedCount: result.deletedCount
                });
            } else {
                console.error('[informedApiHandler] Failed to truncate tu_informed:', result.error);
                res.status(500).json({
                    success: false,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error truncating tu_informed:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error'
            });
        }
    });

    // API endpoint to fill tu_informed from v_informed_upload
    app.post('/api/informed/fill-tu-informed', async (req, res) => {
        try {
            console.log('[informedApiHandler] Filling tu_informed from v_informed_upload');

            const result = await fillOnly();

            if (result.success) {
                console.log('[informedApiHandler] tu_informed filled successfully');
                res.json({
                    success: true,
                    message: 'tu_informed filled successfully',
                    recordCount: result.recordCount
                });
            } else {
                console.error('[informedApiHandler] Failed to fill tu_informed:', result.error);
                res.status(500).json({
                    success: false,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error filling tu_informed:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error'
            });
        }
    });

    // API endpoint to export CSV from tu_informed
    app.post('/api/informed/export-csv', async (req, res) => {
        try {
            console.log('[informedApiHandler] Exporting CSV from tu_informed');

            const result = await exportOnly();

            if (result.success) {
                console.log('[informedApiHandler] CSV exported successfully');
                res.json({
                    success: true,
                    message: 'CSV exported successfully',
                    csvContent: result.csvContent
                });
            } else {
                console.error('[informedApiHandler] Failed to export CSV:', result.error);
                res.status(500).json({
                    success: false,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error exporting CSV:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error'
            });
        }
    });

    // API endpoint to upload CSV to Informed
    app.post('/api/informed/upload-csv', async (req, res) => {
        try {
            console.log('[informedApiHandler] Uploading CSV to Informed');

            const { csvContent } = req.body;

            if (!csvContent) {
                return res.status(400).json({
                    success: false,
                    error: 'CSV content is required'
                });
            }

            const result = await uploadOnly(csvContent);

            if (result.success) {
                console.log('[informedApiHandler] CSV uploaded to Informed successfully');
                res.json({
                    success: true,
                    message: 'CSV uploaded to Informed successfully',
                    result: result.result
                });
            } else {
                console.error('[informedApiHandler] Failed to upload CSV to Informed:', result.error);
                res.status(500).json({
                    success: false,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error uploading CSV to Informed:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error'
            });
        }
    });

    // API endpoint to run the complete upload workflow
    app.post('/api/informed/run-upload-workflow', async (req, res) => {
        try {
            console.log('[informedApiHandler] Running complete Informed upload workflow');

            const result = await runCompleteUploadWorkflow();

            if (result.success) {
                console.log('[informedApiHandler] Complete upload workflow completed successfully');
                res.json({
                    success: true,
                    message: result.message,
                    results: result.results
                });
            } else {
                console.error('[informedApiHandler] Complete upload workflow failed:', result.error);
                res.status(500).json({
                    success: false,
                    error: result.error,
                    results: result.results
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error running complete upload workflow:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error'
            });
        }
    });

    // API endpoint to run complete end-to-end workflow (Download + Import + Upload)
    app.post('/api/informed/run-complete-workflow', async (req, res) => {
        try {
            console.log('[informedApiHandler] Running complete end-to-end workflow (Download + Import + Upload)');

            // Get retry parameters from request or use defaults (same as individual download button)
            const maxRetries = req.body.maxRetries || 30;
            const retryInterval = req.body.retryInterval || 10000; // 10 seconds

            const results = {
                download: null,
                import: null,
                upload: null
            };

            // Phase 1: Download reports (using same logic as individual download button)
            console.log('[informedApiHandler] Phase 1: Downloading reports');
            try {
                if (!informedReportDownloader) {
                    throw new Error('Informed modules not initialized');
                }

                console.log(`[informedApiHandler] Using retry parameters: maxRetries=${maxRetries}, retryInterval=${retryInterval}ms`);
                const downloadResults = await informedReportDownloader.downloadAllReports(maxRetries, retryInterval);

                // Check if all downloads were successful (same logic as individual download)
                const downloadSuccessful = downloadResults.every(result => result.success);

                results.download = {
                    success: downloadSuccessful,
                    reports: downloadResults,
                    error: downloadSuccessful ? null : `Failed to download ${downloadResults.filter(r => !r.success).length} reports`
                };

                if (!downloadSuccessful) {
                    const failedReports = downloadResults.filter(result => !result.success);
                    console.error(`[informedApiHandler] Failed to download ${failedReports.length} reports`);
                    return res.status(500).json({
                        success: false,
                        error: `Failed to download ${failedReports.length} reports`,
                        results
                    });
                }
            } catch (error) {
                console.error('[informedApiHandler] Error in download phase:', error);
                results.download = {
                    success: false,
                    error: error.message
                };
                return res.status(500).json({
                    success: false,
                    error: 'Failed to download reports',
                    results
                });
            }

            // Phase 2: Import reports
            console.log('[informedApiHandler] Phase 2: Importing reports');
            try {
                const importResult = await importAllReports();
                results.import = {
                    success: Array.isArray(importResult) ? importResult.every(r => r.success) : importResult.success,
                    reports: importResult,
                    error: Array.isArray(importResult) ?
                        importResult.filter(r => !r.success).map(r => r.error).join(', ') :
                        importResult.error
                };

                if (!results.import.success) {
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to import reports',
                        results
                    });
                }
            } catch (error) {
                console.error('[informedApiHandler] Error in import phase:', error);
                results.import = {
                    success: false,
                    error: error.message
                };
                return res.status(500).json({
                    success: false,
                    error: 'Failed to import reports',
                    results
                });
            }

            // Phase 3: Upload pricing data
            console.log('[informedApiHandler] Phase 3: Uploading pricing data');
            try {
                const uploadResult = await runCompleteUploadWorkflow();
                results.upload = uploadResult;

                if (!uploadResult.success) {
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to upload pricing data',
                        results
                    });
                }
            } catch (error) {
                console.error('[informedApiHandler] Error in upload phase:', error);
                results.upload = {
                    success: false,
                    error: error.message
                };
                return res.status(500).json({
                    success: false,
                    error: 'Failed to upload pricing data',
                    results
                });
            }

            console.log('[informedApiHandler] Complete end-to-end workflow completed successfully');
            res.json({
                success: true,
                message: 'Complete end-to-end workflow completed successfully',
                results
            });

        } catch (error) {
            console.error('[informedApiHandler] Error running complete end-to-end workflow:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    console.log('[informedApiHandler] Informed API endpoints registered');
}
