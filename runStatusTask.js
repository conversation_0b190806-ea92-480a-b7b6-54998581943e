// runStatusTask.js - Run the enqueueWorkerStatusTask function
import { enqueueWorkerStatusTask } from './enqueueWorkerStatusTask.js';

console.log('Running enqueueWorkerStatusTask...');

enqueueWorkerStatusTask()
  .then(task => {
    console.log('Status update task result:');
    console.dir(task, { depth: null });
    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
