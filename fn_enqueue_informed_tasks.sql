-- Function to enqueue Informed tasks
CREATE OR REPLACE FUNCTION public.fn_enqueue_informed_tasks(
  p_task_type text DEFAULT 'run_informed_process',
  p_payload jsonb DEFAULT '{}',
  p_delay_minutes integer DEFAULT 5
)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
  v_task_id integer;
  v_scheduled_time timestamptz;
BEGIN
  -- Calculate scheduled time
  v_scheduled_time := NOW() + (p_delay_minutes * INTERVAL '1 minute');

  -- Enqueue a task to run the Informed process
  INSERT INTO public.t_task_queue (
    task_type,
    payload,
    status,
    scheduled_at,
    created_at,
    enqueued_by
  )
  VALUES (
    p_task_type,
    p_payload,
    'pending',
    v_scheduled_time,
    NOW(),
    'fn_enqueue_informed_tasks'
  )
  RETURNING id INTO v_task_id;

  RAISE NOTICE 'Enqueued % task with ID % scheduled for %', p_task_type, v_task_id, v_scheduled_time;

  RETURN v_task_id;
END;
$$;
