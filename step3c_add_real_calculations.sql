-- Step 3c: Add real calculations one piece at a time
-- Only run this after step 3b works

-- First, let's add the config cache to the minimal view
CREATE OR REPLACE VIEW v_informed_upload_with_config AS
WITH config AS (
    SELECT * FROM mv_config_cache LIMIT 1
)
SELECT 
    ts.fba_sku as sku,
    '9432'::text as marketplace_id,
    (15.00 - 0.01)::numeric as cost,  -- Still simplified
    'USD'::text as currency,
    15.00::numeric as min_price,
    50.00::numeric as max_price,
    20.00::numeric as map_price,
    'Amazon FBA'::text as listing_type,
    1::integer as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
CROSS JOIN config c
WHERE ts.fba_uploaded_at IS NOT NULL
  AND ts.fba_sku IS NOT NULL

UNION ALL

SELECT 
    ts.fbm_sku as sku,
    '9432'::text as marketplace_id,
    (15.00 - 0.01)::numeric as cost,  -- Still simplified
    'USD'::text as currency,
    15.00::numeric as min_price,
    50.00::numeric as max_price,
    20.00::numeric as map_price,
    'Amazon FBM'::text as listing_type,
    1::integer as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM t_sdasins ts
CROSS JOIN config c
WHERE ts.fbm_uploaded_at IS NOT NULL
  AND ts.fbm_sku IS NOT NULL;

-- Test this version
SELECT COUNT(*) as total_records FROM v_informed_upload_with_config;
