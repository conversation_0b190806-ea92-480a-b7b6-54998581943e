-- Fix the CSV export format to match Informed's expected format

CREATE OR REPLACE FUNCTION public.f_export_tu_informed_to_csv_informed_format()
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    csv_content TEXT;
    header TEXT := '"SKU","MARKETPLACE_ID","COST","CURRENCY","MIN_PRICE","MAX_PRICE","MANAGED","MAP_PRICE","LISTING_TYPE","STRATEGY_ID","DateAdded"';
BEGIN
    -- Generate CSV content with proper format for Informed
    csv_content := (
        SELECT header || E'\n' || string_agg(
            format('"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"',
                sku, 
                marketplace_id, 
                cost, 
                currency, 
                min_price, 
                max_price, 
                managed,  -- MANAGED comes before MAP_PRICE
                COALESCE(map_price, ''), -- Handle NULL values
                listing_type, 
                strategy_id, 
                to_char(dateadded AT TIME ZONE 'UTC', 'MM/DD/YYYY HH24:MI:SS') -- Format date like 3/10/2024 9:48:27
            ),
            E'\n'
        )
        FROM tu_informed
        ORDER BY sku -- Ensure consistent ordering
    );

    -- Check if CSV content is empty
    IF csv_content IS NULL OR csv_content = '' THEN
        RAISE EXCEPTION 'No data found in tu_informed';
    END IF;

    -- Return the CSV content
    RETURN csv_content;
END;
$$;

-- Test the new function
SELECT f_export_tu_informed_to_csv_informed_format() LIMIT 1;

-- Show just the header and first few lines to verify format
SELECT 
    split_part(f_export_tu_informed_to_csv_informed_format(), E'\n', 1) as header,
    split_part(f_export_tu_informed_to_csv_informed_format(), E'\n', 2) as first_row;
