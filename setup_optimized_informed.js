import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupOptimizedInformed() {
    console.log('🚀 Setting up optimized Informed system with real pricing calculations...');
    
    try {
        // Step 1: Run the main setup SQL
        console.log('📋 Step 1: Creating materialized views and optimized views...');
        const setupSql = fs.readFileSync('setup_optimized_informed_system.sql', 'utf8');
        
        const { error: setupError } = await supabase.rpc('exec_sql', { sql: setupSql });
        
        if (setupError) {
            console.error('❌ Error in setup SQL:', setupError);
            return;
        }
        
        console.log('✅ Step 1 completed: Views and materialized views created');
        
        // Step 2: Run the functions SQL
        console.log('📋 Step 2: Creating functions...');
        const functionsSql = fs.readFileSync('setup_optimized_functions.sql', 'utf8');
        
        const { error: functionsError } = await supabase.rpc('exec_sql', { sql: functionsSql });
        
        if (functionsError) {
            console.error('❌ Error in functions SQL:', functionsError);
            return;
        }
        
        console.log('✅ Step 2 completed: Functions created');
        
        // Step 3: Test the setup
        console.log('📋 Step 3: Testing the setup...');
        
        const { data: countResult, error: countError } = await supabase.rpc('fn_count_v_informed_upload_optimized');
        
        if (countError) {
            console.error('❌ Error testing optimized function:', countError);
            return;
        }
        
        console.log(`✅ Step 3 completed: Found ${countResult} records in optimized view`);
        
        // Step 4: Test a sample of the data
        console.log('📋 Step 4: Checking sample data...');
        
        const { data: sampleData, error: sampleError } = await supabase
            .from('v_informed_upload_optimized')
            .select('sku, min_price, max_price, map_price')
            .limit(5);
        
        if (sampleError) {
            console.error('❌ Error getting sample data:', sampleError);
            return;
        }
        
        console.log('✅ Sample data from optimized view:');
        console.table(sampleData);
        
        console.log('\n🎉 SUCCESS! Optimized Informed system is now set up!');
        console.log('🎯 The system will now use REAL pricing calculations instead of hardcoded values');
        console.log('📊 Next: Run the Informed workflow through the admin interface to test');
        
    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

// Run the setup
setupOptimizedInformed();
