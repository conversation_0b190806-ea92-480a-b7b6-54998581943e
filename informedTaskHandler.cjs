/**
 * Informed Task Handler
 * 
 * This module provides task handlers for the Informed process.
 * It is imported by taskQueueWorker.js to handle Informed-related tasks.
 */

const { downloadAllReports, getReportsStatus, REPORT_TYPES } = require('./informedReportDownloader.cjs');
const { importAllReports, importReport } = require('./informedReportImporter.cjs');
const { runFullProcess, startScheduler, stopScheduler, getSchedulerStatus, initialize } = require('./informedScheduler.cjs');

/**
 * Handle the download_informed_reports task
 * @param {Object} task - Task object from the queue
 * @returns {Promise<Object>} - Result of the task
 */
async function handleDownloadInformedReports(task) {
    console.log(`[informedTaskHandler] Processing download_informed_reports task ${task.id}`);
    
    try {
        const results = await downloadAllReports();
        
        // Check if all downloads were successful
        const allSuccessful = results.every(result => result.success);
        
        if (allSuccessful) {
            return {
                success: true,
                message: 'Successfully downloaded all Informed reports',
                details: results
            };
        } else {
            const failedReports = results.filter(result => !result.success);
            return {
                success: false,
                message: `Failed to download ${failedReports.length} Informed reports`,
                details: results
            };
        }
    } catch (error) {
        console.error('[informedTaskHandler] Error downloading Informed reports:', error);
        return {
            success: false,
            message: `Error downloading Informed reports: ${error.message}`,
            error: error.message
        };
    }
}

/**
 * Handle the import_informed_reports task
 * @param {Object} task - Task object from the queue
 * @returns {Promise<Object>} - Result of the task
 */
async function handleImportInformedReports(task) {
    console.log(`[informedTaskHandler] Processing import_informed_reports task ${task.id}`);
    
    try {
        const results = await importAllReports();
        
        // Check if all imports were successful
        const allSuccessful = results.every(result => result.success);
        
        if (allSuccessful) {
            return {
                success: true,
                message: 'Successfully imported all Informed reports',
                details: results
            };
        } else {
            const failedReports = results.filter(result => !result.success);
            return {
                success: false,
                message: `Failed to import ${failedReports.length} Informed reports`,
                details: results
            };
        }
    } catch (error) {
        console.error('[informedTaskHandler] Error importing Informed reports:', error);
        return {
            success: false,
            message: `Error importing Informed reports: ${error.message}`,
            error: error.message
        };
    }
}

/**
 * Handle the run_informed_process task
 * @param {Object} task - Task object from the queue
 * @returns {Promise<Object>} - Result of the task
 */
async function handleRunInformedProcess(task) {
    console.log(`[informedTaskHandler] Processing run_informed_process task ${task.id}`);
    
    try {
        const result = await runFullProcess();
        
        if (result.success) {
            return {
                success: true,
                message: 'Successfully ran full Informed process',
                details: result
            };
        } else {
            return {
                success: false,
                message: `Failed to run full Informed process: ${result.error}`,
                details: result
            };
        }
    } catch (error) {
        console.error('[informedTaskHandler] Error running full Informed process:', error);
        return {
            success: false,
            message: `Error running full Informed process: ${error.message}`,
            error: error.message
        };
    }
}

// Export the task handlers
module.exports = {
    handleDownloadInformedReports,
    handleImportInformedReports,
    handleRunInformedProcess
};
