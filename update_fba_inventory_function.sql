-- Updated function to handle chunking for large datasets
-- This replaces the existing fn_upsert_monthly_fba_inventory_value function

-- First, drop the existing function since we're changing the return type
DROP FUNCTION IF EXISTS fn_upsert_monthly_fba_inventory_value();

-- Now create the new function with table return type
CREATE OR REPLACE FUNCTION fn_upsert_monthly_fba_inventory_value()
RETURNS TABLE(
  processed_records integer,
  total_units integer,
  total_value numeric(12, 2),
  weighted_avg_cost numeric(10, 2),
  report_month date,
  processing_time interval
)
LANGUAGE plpgsql
AS $$
DECLARE
  latest_date text;
  calc_report_month date;
  chunk_size integer := 1000;
  offset_val integer := 0;
  chunk_count integer := 0;
  total_records integer := 0;
  running_units integer := 0;
  running_value numeric(12, 2) := 0;
  final_weighted_cost numeric(10, 2) := 0;
  start_time timestamp := now();
  chunk_units integer;
  chunk_value numeric(12, 2);
BEGIN
  -- Step 1: Find most recent Date string from Amazon data
  SELECT max("Date") INTO latest_date FROM it_amaz_monthly_inventory_ledger_summary;
  
  IF latest_date IS NULL THEN
    RAISE EXCEPTION 'No data found in it_amaz_monthly_inventory_ledger_summary';
  END IF;

  -- Step 2: Convert to last day of month from MM/YYYY format
  -- Parse MM/YYYY (e.g., "04/2025") to get the last day of that month
  -- Split the date string and construct the last day of the month
  SELECT (
    CASE
      WHEN latest_date ~ '^\d{2}/\d{4}$' THEN
        -- Format is MM/YYYY, convert to last day of month
        (date_trunc('month', make_date(
          split_part(latest_date, '/', 2)::integer,  -- year
          split_part(latest_date, '/', 1)::integer,  -- month
          1                                          -- day
        )) + interval '1 month - 1 day')::date
      ELSE
        -- Fallback: try to parse as-is and get last day of month
        (date_trunc('month', to_date(latest_date, 'MM/YYYY')) + interval '1 month - 1 day')::date
    END
  ) INTO calc_report_month;

  -- Debug logging
  RAISE NOTICE 'Latest date string: %, Calculated report month: %', latest_date, calc_report_month;

  -- Step 3: Get total count for progress tracking
  SELECT count(*) INTO total_records
  FROM it_amaz_monthly_inventory_ledger_summary i
  JOIN t_sdasins s ON i."MSKU" = s.fba_sku
  WHERE i."Date" = latest_date;

  -- Step 4: Process data in chunks
  LOOP
    -- Process current chunk
    WITH matched_chunk AS (
      SELECT
        s.id as sdasin_id,
        i."Ending_Warehouse_Balance" as units,
        COALESCE(m.avg_carrying_cost_fba, 5.00) as unit_cost -- Default to $5.00 if no cost data
      FROM
        it_amaz_monthly_inventory_ledger_summary i
        JOIN t_sdasins s ON i."MSKU" = s.fba_sku
        LEFT JOIN mv_sdasin_avg_carrying_cost_fba m ON s.id = m.sdasin_id
      WHERE
        i."Date" = latest_date
      ORDER BY s.id
      LIMIT chunk_size OFFSET offset_val
    )
    SELECT
      COALESCE(sum(units), 0),
      COALESCE(sum(units * unit_cost), 0)
    INTO chunk_units, chunk_value
    FROM matched_chunk;

    -- Exit if no more records
    EXIT WHEN chunk_units = 0 AND chunk_value = 0;

    -- Add to running totals
    running_units := running_units + chunk_units;
    running_value := running_value + chunk_value;
    
    -- Increment counters
    chunk_count := chunk_count + 1;
    offset_val := offset_val + chunk_size;
    
    -- Log progress (optional, can be removed for production)
    RAISE NOTICE 'Processed chunk % of %, running totals: % units, $%', 
      chunk_count, CEIL(total_records::numeric / chunk_size), running_units, running_value;
  END LOOP;

  -- Step 5: Calculate weighted average cost
  IF running_units > 0 THEN
    final_weighted_cost := ROUND((running_value / running_units)::numeric, 2);
  ELSE
    final_weighted_cost := 0;
  END IF;

  -- Step 6: Upsert summary row
  INSERT INTO rpt_amaz_monthly_fba_inventory_value (
    month, total_ending_units, sum_of_extended_values, avg_unit_cost
  )
  VALUES (
    calc_report_month, running_units, running_value, final_weighted_cost
  )
  ON CONFLICT (month) DO UPDATE
    SET total_ending_units = EXCLUDED.total_ending_units,
        sum_of_extended_values = EXCLUDED.sum_of_extended_values,
        avg_unit_cost = EXCLUDED.avg_unit_cost,
        updated_at = now()
  WHERE rpt_amaz_monthly_fba_inventory_value.locked_at IS NULL;

  -- Return results
  RETURN QUERY SELECT 
    total_records,
    running_units,
    running_value,
    final_weighted_cost,
    calc_report_month,
    (now() - start_time)::interval;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION fn_upsert_monthly_fba_inventory_value() TO service_role;
GRANT EXECUTE ON FUNCTION fn_upsert_monthly_fba_inventory_value() TO authenticated;
