// checkStatusTasks.js - Check for worker_daemon_online tasks
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Using SUPABASE_KEY instead of SUPABASE_SERVICE_ROLE_KEY
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStatusTasks() {
  try {
    console.log('Checking for worker_daemon_online tasks...');

    // First check for worker_daemon_online tasks
    const { data: statusData, error: statusError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('task_type', 'worker_daemon_online')
      .order('created_at', { ascending: false })
      .limit(10);

    if (statusError) {
      console.error('Error querying status tasks:', statusError.message);
    } else if (statusData && statusData.length > 0) {
      console.log(`Found ${statusData.length} worker_daemon_online tasks:`);
      statusData.forEach(task => {
        console.log(`- ID: ${task.id}, Created: ${task.created_at}, Result: ${task.result}`);
      });

      // Check time gaps between tasks
      if (statusData.length > 1) {
        console.log('\nTime gaps between tasks:');
        for (let i = 0; i < statusData.length - 1; i++) {
          const current = new Date(statusData[i].created_at);
          const next = new Date(statusData[i + 1].created_at);
          const diffMs = current - next;
          const diffMins = Math.round(diffMs / (1000 * 60));
          console.log(`- Between ${statusData[i].id} and ${statusData[i + 1].id}: ${diffMins} minutes`);
        }
      }
    } else {
      console.log('No worker_daemon_online tasks found.');
    }

    // Now check for the most recent tasks of any type
    console.log('\nChecking for most recent tasks of any type...');
    const { data: recentData, error: recentError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, created_at, status')
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.error('Error querying recent tasks:', recentError.message);
    } else if (recentData && recentData.length > 0) {
      console.log(`Found ${recentData.length} recent tasks:`);
      recentData.forEach(task => {
        console.log(`- ID: ${task.id}, Type: ${task.task_type}, Created: ${task.created_at}, Status: ${task.status}`);
      });
    } else {
      console.log('No recent tasks found.');
    }
  } catch (err) {
    console.error('Exception while checking tasks:', err.message);
  }
}

// Run the function
checkStatusTasks()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
