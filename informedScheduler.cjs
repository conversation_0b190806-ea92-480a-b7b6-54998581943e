/**
 * Informed Scheduler
 *
 * This script schedules the download and import of Informed reports at specified times.
 * It uses node-cron to schedule the tasks and runs them at 6:30 AM, 12:30 PM, and 6:30 PM CST.
 */

const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const cronParser = require('cron-parser');
const { downloadAllReports } = require('./informedReportDownloader.cjs');
const { importAllReports } = require('./informedReportImporter.cjs');

// Constants
const CONFIG_FILE = path.join(__dirname, 'informedSchedulerConfig.json');
const DEFAULT_CONFIG = {
    enabled: false,
    schedule: '30 6,12,18 * * *', // 6:30 AM, 12:30 PM, 6:30 PM every day
    lastRun: null,
    nextRun: null
};

// Global variables
let scheduler = null;

/**
 * Load the scheduler configuration
 * @returns {Object} - Scheduler configuration
 */
function loadConfig() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
            return { ...DEFAULT_CONFIG, ...config };
        }
    } catch (error) {
        console.error('Error loading scheduler config:', error);
    }

    return DEFAULT_CONFIG;
}

/**
 * Save the scheduler configuration
 * @param {Object} config - Scheduler configuration to save
 */
function saveConfig(config) {
    try {
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    } catch (error) {
        console.error('Error saving scheduler config:', error);
    }
}

/**
 * Calculate the next run time based on the cron schedule
 * @param {string} schedule - Cron schedule expression
 * @returns {Date} - Next run time
 */
function calculateNextRun(schedule) {
    try {
        const interval = cronParser.parseExpression(schedule);
        return interval.next().toDate();
    } catch (error) {
        console.error('Error calculating next run time:', error);
        return null;
    }
}

/**
 * Run the full Informed process (download and import)
 * @param {number} maxRetries - Maximum number of retries for checking status
 * @param {number} retryInterval - Interval between retries in milliseconds
 * @returns {Promise<Object>} - Results of the process
 */
async function runFullProcess(maxRetries = 30, retryInterval = 10000) {
    console.log(`Running full Informed process (maxRetries: ${maxRetries}, retryInterval: ${retryInterval}ms)...`);

    try {
        // Download reports
        const downloadResults = await downloadAllReports(maxRetries, retryInterval);

        // Import reports
        const importResults = await importAllReports();

        // Combine results
        const combinedResults = downloadResults.map(downloadResult => {
            const importResult = importResults.find(ir => ir.report === downloadResult.report);
            return {
                report: downloadResult.report,
                downloadStatus: downloadResult.success ? 'Success' : 'Failed',
                downloadError: downloadResult.error,
                importStatus: importResult ? (importResult.success ? 'Success' : 'Failed') : 'Not Attempted',
                importError: importResult ? importResult.error : null,
                recordCount: importResult ? importResult.recordCount : 0
            };
        });

        // Update config with last run time
        const config = loadConfig();
        config.lastRun = new Date().toISOString();
        const nextRunDate = calculateNextRun(config.schedule);
        config.nextRun = nextRunDate ? nextRunDate.toISOString() : null;
        saveConfig(config);

        return {
            success: true,
            reports: combinedResults
        };
    } catch (error) {
        console.error('Error running full Informed process:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Start the scheduler
 * @returns {Object} - Result of starting the scheduler
 */
function startScheduler() {
    const config = loadConfig();

    if (scheduler) {
        return {
            success: true,
            message: 'Scheduler is already running',
            nextRun: config.nextRun,
            schedule: config.schedule
        };
    }

    try {
        scheduler = cron.schedule(config.schedule, async () => {
            console.log('Running scheduled Informed process...');
            await runFullProcess();
        });

        config.enabled = true;
        config.nextRun = calculateNextRun(config.schedule).toISOString();
        saveConfig(config);

        console.log(`Scheduler started with schedule: ${config.schedule}`);
        console.log(`Next run: ${new Date(config.nextRun).toLocaleString()}`);

        return {
            success: true,
            message: 'Scheduler started successfully',
            nextRun: config.nextRun,
            schedule: config.schedule
        };
    } catch (error) {
        console.error('Error starting scheduler:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Stop the scheduler
 * @returns {Object} - Result of stopping the scheduler
 */
function stopScheduler() {
    if (!scheduler) {
        return {
            success: true,
            message: 'Scheduler is not running'
        };
    }

    try {
        scheduler.stop();
        scheduler = null;

        const config = loadConfig();
        config.enabled = false;
        saveConfig(config);

        console.log('Scheduler stopped');

        return {
            success: true,
            message: 'Scheduler stopped successfully'
        };
    } catch (error) {
        console.error('Error stopping scheduler:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get the current status of the scheduler
 * @returns {Object} - Scheduler status
 */
function getSchedulerStatus() {
    const config = loadConfig();

    return {
        enabled: config.enabled && scheduler !== null,
        schedule: config.schedule,
        lastRun: config.lastRun,
        nextRun: config.nextRun
    };
}

/**
 * Initialize the scheduler
 */
function initialize() {
    const config = loadConfig();

    if (config.enabled) {
        startScheduler();
    }
}

// Export functions for use in other modules
module.exports = {
    runFullProcess,
    startScheduler,
    stopScheduler,
    getSchedulerStatus,
    initialize
};

// If this script is run directly, initialize the scheduler
if (require.main === module) {
    initialize();
}
