// processRenameAndUploadImagesTask.js - Process rename_and_upload_images tasks
import { renameAndUploadImages } from './renameImages.js';

/**
 * Process a rename_and_upload_images task
 * @param {Object} task - The task object from the task queue
 * @param {Object} options - Options including supabase client and helper functions
 * @returns {Promise<void>}
 */
async function processRenameAndUploadImagesTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processRenameAndUploadImagesTask.js] Processing task ${task.id} of type ${task.task_type}`);

  const startTime = new Date();

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        payload = task.payload;
      }
    } catch (err) {
      const errMsg = `[processRenameAndUploadImagesTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Parsing payload for task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Failed to parse task payload",
        error: err.message
      });
      return;
    }

    // Validate the payload
    if (!payload || !payload.folderId) {
      const errMsg = `[processRenameAndUploadImagesTask.js] Invalid payload for task ${task.id}: Missing folderId`;
      console.error(errMsg);
      await logError(errMsg, `Validating payload for task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Invalid payload: Missing folderId",
        payload: payload
      });
      return;
    }

    const folderId = payload.folderId;
    console.log(`[processRenameAndUploadImagesTask.js] Processing folder ID: ${folderId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Execute the rename and upload process
    const result = await renameAndUploadImages(folderId);

    // Calculate processing time
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    // Update task status based on result
    if (result.success) {
      // Special case for when the folder doesn't exist
      if (result.noFolder) {
        await updateTaskStatus(task.id, 'completed', {
          message: "No folder or images to process.",
          folder_id: folderId,
          processing_time_ms: processingTimeMs,
          processing_time_sec: parseFloat(processingTimeSec)
        });
      } else {
        // Normal success case
        const taskResult = {
          message: `Successfully processed folder ${folderId}`,
          folder_id: folderId,
          primary_dir_exists: result.primaryDirExists,
          backup_dir_exists: result.backupDirExists,
          files_moved: result.filesMoved || 0,
          files_conflicted: result.filesConflicted || 0,
          renamed_count: result.renamedCount,
          s3_upload_count: result.s3UploadCount,
          highest_existing_index: result.highestExistingIndex,
          first_available_index: result.firstAvailableIndex,
          start_index_used: result.startIndexUsed,
          processing_time_ms: processingTimeMs,
          processing_time_sec: parseFloat(processingTimeSec)
        };

        // Include existing indices if available
        if (result.existingIndices && result.existingIndices.length > 0) {
          taskResult.existing_indices = result.existingIndices;
        }

        // Add first and last renamed files if available
        if (result.renamedCount > 0 && result.firstRenamedFile && result.lastRenamedFile) {
          taskResult.first_renamed_file = result.firstRenamedFile;
          taskResult.last_renamed_file = result.lastRenamedFile;
        }

        await updateTaskStatus(task.id, 'completed', taskResult);
      }
    } else {
      // Error case
      await updateTaskStatus(task.id, 'error', {
        message: `Error processing folder ${folderId}: ${result.error}`,
        folder_id: folderId,
        error: result.error,
        renamed_count: result.renamedCount || 0,
        local_backup_count: result.localBackupCount || 0,
        s3_upload_count: result.s3UploadCount || 0,
        processing_time_ms: processingTimeMs,
        processing_time_sec: parseFloat(processingTimeSec)
      });
    }
  } catch (err) {
    // Calculate processing time even for errors
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    const errMsg = `[processRenameAndUploadImagesTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process rename and upload task due to an unexpected error",
      error: err.message,
      processing_time_ms: processingTimeMs,
      processing_time_sec: parseFloat(processingTimeSec)
    });
  }
}

export default processRenameAndUploadImagesTask;
