// enqueueWorkerStatusTask.js - Function to enqueue a worker daemon status update task
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Using SUPABASE_KEY instead of SUPABASE_SERVICE_ROLE_KEY

// Log connection details when module is loaded
console.log('[enqueueWorkerStatusTask.js] Supabase URL:', supabaseUrl);
console.log('[enqueueWorkerStatusTask.js] Supabase key defined:', supabaseKey ? 'Yes' : 'No');

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Enqueues a worker_daemon_online status update task
 * This task serves as a heartbeat to indicate the worker daemon is running
 * @returns {Promise<Object>} The created task data
 */
export async function enqueueWorkerStatusTask() {
  try {
    console.log(`[enqueueWorkerStatusTask.js] Enqueueing worker_daemon_online status update task`);
    console.log(`[enqueueWorkerStatusTask.js] Using Supabase URL: ${supabaseUrl}`);
    console.log(`[enqueueWorkerStatusTask.js] Supabase key defined: ${supabaseKey ? 'Yes' : 'No'}`);

    const now = new Date();
    const payload = "Status Update";

    // Format the date in CST (Central Standard Time)
    const cstOptions = {
      timeZone: 'America/Chicago',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    };
    const cstTimeString = now.toLocaleString('en-US', cstOptions);
    const result = `Worker Daemon is up and running as of ${cstTimeString} (CST)`;

    console.log(`[enqueueWorkerStatusTask.js] Preparing to insert task with payload: ${payload}`);
    console.log(`[enqueueWorkerStatusTask.js] Result will be: ${result}`);

    // For database timestamps, we still need to use ISO format
    // but we'll use the CST time in the result message
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'worker_daemon_online',
        status: 'status_update', // Status update instead of complete
        payload: JSON.stringify(payload),
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        processed_at: now.toISOString(),
        result: JSON.stringify(result)
      })
      .select();

    if (error) {
      console.error(`[enqueueWorkerStatusTask.js] Error enqueueing status update task: ${error.message}`);
      console.error(`[enqueueWorkerStatusTask.js] Error details:`, error);
      return null;
    }

    if (!data || data.length === 0) {
      console.error(`[enqueueWorkerStatusTask.js] No data returned from insert operation`);
      return null;
    }

    console.log(`[enqueueWorkerStatusTask.js] Successfully enqueued status update task with ID ${data[0].id}`);
    return data[0];
  } catch (err) {
    console.error(`[enqueueWorkerStatusTask.js] Exception while enqueueing status update task: ${err.message}`);
    console.error(`[enqueueWorkerStatusTask.js] Stack trace:`, err.stack);
    return null;
  }
}

// If this script is run directly (not imported), execute the main function
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const isMainScript = process.argv[1] === __filename || process.argv[1] === path.resolve(__filename);

if (isMainScript) {
  console.log('[enqueueWorkerStatusTask.js] Running as main script');

  enqueueWorkerStatusTask()
    .then(task => {
      console.log('[enqueueWorkerStatusTask.js] Status update task result:');
      console.dir(task, { depth: null });
      process.exit(0);
    })
    .catch(err => {
      console.error('[enqueueWorkerStatusTask.js] Error:', err);
      process.exit(1);
    });
} else {
  console.log('[enqueueWorkerStatusTask.js] Loaded as module');
}
