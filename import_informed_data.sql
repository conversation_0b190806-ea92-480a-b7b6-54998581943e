-- Import Informed Repricer data from CSV files
-- This script assumes the CSV files are in the following locations:
-- 1. All Fields: C:\Users\<USER>\supabase_project\data\external data\from_informed_all_fields.csv
-- 2. Competition Landscape: C:\Users\<USER>\supabase_project\data\external data\from_informed_competition_landscape.csv
-- 3. No Buy Box: C:\Users\<USER>\supabase_project\data\external data\from_informed_no_buy_box.csv

-- First, truncate the tables
TRUNCATE TABLE public.it_infor_all_fields;
TRUNCATE TABLE public.it_infor_competition_landscape;
TRUNCATE TABLE public.it_infor_no_buy_box;

-- NOTE: This script must be run with psql's \i command, not directly with psql -f
-- The \copy commands below must be run from psql, not as regular SQL

-- Import All Fields data
\echo 'Importing All Fields data...'
\copy public.it_infor_all_fields ("SKU", "STOCK", "ITEM_ID", "MEMO", "TITLE", "MARKE<PERSON>LACE_ID", "COST", "CURRENCY", "CURRENT_VELOCITY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "CURRENT_SHIPPING", "MANUAL_PRICE", "ORIGINAL_PRICE", "MAP_PRICE", "LISTING_TYPE", "STRATEGY_ID", "BUY_BOX_PRICE", "BUYBOX_SELLER", "BUYBOX_WINNER", "VAT_PERCENTAGE", "DATE_ADDED", "STOCK_COST_VALUE", "DAYS_SINCE_BUYBOX", "DATE_OF_LAST_SALE", "DAYS_REMAINING", "AVAILABILITY", "OLDEST_STOCK_DATE") FROM 'C:\Users\<USER>\supabase_project\data\external data\from_informed_all_fields.csv' WITH (FORMAT csv, HEADER true);

-- Import Competition Landscape data
\echo 'Importing Competition Landscape data...'
\copy public.it_infor_competition_landscape ("SKU", "ITEM_ID", "STOCK", "MARKETPLACE_ID", "COST", "CURRENCY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "COMP_PRICE", "COMP_ID", "COMP_LISTING_TYPE", "BUYBOX_PRICE", "BUYBOX_SELLER", "BUYBOX_WINNER", "STRATEGY_ID") FROM 'C:\Users\<USER>\supabase_project\data\external data\from_informed_competition_landscape.csv' WITH (FORMAT csv, HEADER true);

-- Import No Buy Box data
\echo 'Importing No Buy Box data...'
\copy public.it_infor_no_buy_box ("SKU", "ITEM_ID", "TITLE", "MARKETPLACE_ID", "COST", "CURRENCY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "BUY_BOX_PRICE", "BUY_BOX_WINNER", "STRATEGY_ID", "Fulfillment Type", "Marketplace ID") FROM 'C:\Users\<USER>\supabase_project\data\external data\from_informed_no_buy_box.csv' WITH (FORMAT csv, HEADER true);

-- Update the created_at timestamp
UPDATE public.it_infor_all_fields SET created_at = NOW();
UPDATE public.it_infor_competition_landscape SET created_at = NOW();
UPDATE public.it_infor_no_buy_box SET created_at = NOW();

-- Log the import
INSERT INTO public.t_task_queue (
  task_type,
  payload,
  status,
  result,
  scheduled_at,
  created_at,
  completed_at,
  enqueued_by
) VALUES (
  'informed_import_completed',
  jsonb_build_object(
    'all_fields_count', (SELECT COUNT(*) FROM public.it_infor_all_fields),
    'competition_landscape_count', (SELECT COUNT(*) FROM public.it_infor_competition_landscape),
    'no_buy_box_count', (SELECT COUNT(*) FROM public.it_infor_no_buy_box)
  ),
  'completed',
  'Imported Informed Repricer data via SQL script',
  NOW(),
  NOW(),
  NOW(),
  'import_informed_data.sql'
);

-- Print the results
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_all_fields' FROM public.it_infor_all_fields;
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_competition_landscape' FROM public.it_infor_competition_landscape;
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_no_buy_box' FROM public.it_infor_no_buy_box;
