-- Simple function to count records in v_informed_upload
-- This helps us check data size without timing out

CREATE OR REPLACE FUNCTION public.fn_count_v_informed_upload()
RETURNS integer AS $$
DECLARE
    record_count integer;
BEGIN
    SELECT COUNT(*) INTO record_count FROM v_informed_upload;
    RETURN record_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN -1; -- Return -1 to indicate error
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
