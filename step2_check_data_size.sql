-- Step 2: Check data sizes before creating expensive materialized views
-- Run this to see how much data we're dealing with

-- Check how many sdasins we have
SELECT COUNT(*) as total_sdasins FROM t_sdasins;

-- Check how many have FBA/FBM uploads
SELECT 
    COUNT(*) FILTER (WHERE fba_uploaded_at IS NOT NULL) as fba_count,
    COUNT(*) FILTER (WHERE fbm_uploaded_at IS NOT NULL) as fbm_count,
    COUNT(*) as total_sdasins
FROM t_sdasins;

-- Check the size of the disc joins table
SELECT COUNT(*) as total_disc_joins FROM tjoin_discs_sdasins;

-- Check how many unique sdasin_ids we need to process
SELECT COUNT(DISTINCT sdasin_id) as unique_sdasins_with_discs 
FROM tjoin_discs_sdasins;

-- This will help us decide if we need to create the disc counts materialized view
-- or if we can use a simpler approach
