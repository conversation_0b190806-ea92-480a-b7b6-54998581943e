-- Function to generate historical FBA inventory value reports for all months
-- This processes all months that have data in it_amaz_monthly_inventory_ledger_summary

-- First, drop the existing function since we're changing the return type
DROP FUNCTION IF EXISTS fn_generate_historical_fba_inventory_values();

-- Now create the new function with updated return type
CREATE OR REPLACE FUNCTION fn_generate_historical_fba_inventory_values()
RETURNS TABLE(
  months_processed integer,
  months_locked integer,
  total_records_processed integer,
  processing_time interval,
  details text
)
LANGUAGE plpgsql
AS $$
DECLARE
  month_record RECORD;
  calc_report_month date;
  chunk_size integer := 1000;
  offset_val integer := 0;
  chunk_count integer := 0;
  month_total_records integer := 0;
  running_units integer := 0;
  running_value numeric(12, 2) := 0;
  final_weighted_cost numeric(10, 2) := 0;
  chunk_units integer;
  chunk_value numeric(12, 2);
  start_time timestamp := now();
  processed_months integer := 0;
  locked_months integer := 0;
  total_all_records integer := 0;
  result_details text := '';
  existing_locked boolean := false;
BEGIN
  -- Get all distinct months from the Amazon data
  FOR month_record IN
    SELECT DISTINCT
      "Date" as month_date,
      -- Convert MM/YYYY to sortable format for proper ordering
      CASE
        WHEN "Date" ~ '^\d{2}/\d{4}$' THEN
          split_part("Date", '/', 2) || '-' || split_part("Date", '/', 1)  -- Convert to YYYY-MM for sorting
        ELSE "Date"
      END as sort_key
    FROM it_amaz_monthly_inventory_ledger_summary
    ORDER BY sort_key ASC
  LOOP
    -- Convert month string to last day of month
    SELECT (
      CASE
        WHEN month_record.month_date ~ '^\d{2}/\d{4}$' THEN
          -- Format is MM/YYYY, convert to last day of month
          (date_trunc('month', make_date(
            split_part(month_record.month_date, '/', 2)::integer,  -- year
            split_part(month_record.month_date, '/', 1)::integer,  -- month
            1                                                      -- day
          )) + interval '1 month - 1 day')::date
        ELSE
          -- Fallback: try to parse as-is and get last day of month
          (date_trunc('month', to_date(month_record.month_date, 'MM/YYYY')) + interval '1 month - 1 day')::date
      END
    ) INTO calc_report_month;

    -- Check if this month is already locked
    SELECT (locked_at IS NOT NULL) INTO existing_locked
    FROM rpt_amaz_monthly_fba_inventory_value
    WHERE month = calc_report_month;

    -- Reset counters for this month
    running_units := 0;
    running_value := 0;
    offset_val := 0;
    chunk_count := 0;

    -- Get total count for this month
    SELECT count(*) INTO month_total_records
    FROM it_amaz_monthly_inventory_ledger_summary i
    JOIN t_sdasins s ON i."MSKU" = s.fba_sku
    WHERE i."Date" = month_record.month_date;

    RAISE NOTICE 'Processing month %: % records', month_record.month_date, month_total_records;

    -- Process data in chunks for this month
    LOOP
      -- Process current chunk
      WITH matched_chunk AS (
        SELECT
          s.id as sdasin_id,
          i."Ending_Warehouse_Balance" as units,
          COALESCE(m.avg_carrying_cost_fba, 5.00) as unit_cost -- Default to $5.00 if no cost data
        FROM
          it_amaz_monthly_inventory_ledger_summary i
          JOIN t_sdasins s ON i."MSKU" = s.fba_sku
          LEFT JOIN mv_sdasin_avg_carrying_cost_fba m ON s.id = m.sdasin_id
        WHERE
          i."Date" = month_record.month_date
        ORDER BY s.id
        LIMIT chunk_size OFFSET offset_val
      )
      SELECT
        COALESCE(sum(units), 0),
        COALESCE(sum(units * unit_cost), 0)
      INTO chunk_units, chunk_value
      FROM matched_chunk;

      -- Exit if no more records
      EXIT WHEN chunk_units = 0 AND chunk_value = 0;

      -- Add to running totals
      running_units := running_units + chunk_units;
      running_value := running_value + chunk_value;
      
      -- Increment counters
      chunk_count := chunk_count + 1;
      offset_val := offset_val + chunk_size;
    END LOOP;

    -- Calculate weighted average cost for this month
    IF running_units > 0 THEN
      final_weighted_cost := ROUND((running_value / running_units)::numeric, 2);
    ELSE
      final_weighted_cost := 0;
    END IF;

    -- Upsert summary row for this month
    -- Handle locked vs unlocked records differently
    IF existing_locked THEN
      -- For locked records, only update the _new columns for comparison
      INSERT INTO rpt_amaz_monthly_fba_inventory_value (
        month, total_ending_units, sum_of_extended_values, avg_unit_cost,
        total_ending_units_new, sum_of_extended_values_new, avg_unit_cost_new
      )
      VALUES (
        calc_report_month, running_units, running_value, final_weighted_cost,
        running_units, running_value, final_weighted_cost
      )
      ON CONFLICT (month) DO UPDATE
        SET total_ending_units_new = EXCLUDED.total_ending_units_new,
            sum_of_extended_values_new = EXCLUDED.sum_of_extended_values_new,
            avg_unit_cost_new = EXCLUDED.avg_unit_cost_new,
            updated_at = now()
      WHERE rpt_amaz_monthly_fba_inventory_value.locked_at IS NOT NULL;

      result_details := result_details || 'LOCKED ' || month_record.month_date ||
                       ': ' || month_total_records || ' records, ' ||
                       running_units || ' units, $' || running_value || ' (updated _new columns only)' || E'\n';
      locked_months := locked_months + 1;
    ELSE
      -- For unlocked records, update the main columns (and optionally clear _new columns)
      INSERT INTO rpt_amaz_monthly_fba_inventory_value (
        month, total_ending_units, sum_of_extended_values, avg_unit_cost
      )
      VALUES (
        calc_report_month, running_units, running_value, final_weighted_cost
      )
      ON CONFLICT (month) DO UPDATE
        SET total_ending_units = EXCLUDED.total_ending_units,
            sum_of_extended_values = EXCLUDED.sum_of_extended_values,
            avg_unit_cost = EXCLUDED.avg_unit_cost,
            total_ending_units_new = NULL,
            sum_of_extended_values_new = NULL,
            avg_unit_cost_new = NULL,
            updated_at = now()
      WHERE rpt_amaz_monthly_fba_inventory_value.locked_at IS NULL;

      result_details := result_details || 'PROCESSED ' || month_record.month_date ||
                       ': ' || month_total_records || ' records, ' ||
                       running_units || ' units, $' || running_value || E'\n';
    END IF;

    -- Track progress
    processed_months := processed_months + 1;
    total_all_records := total_all_records + month_total_records;

    RAISE NOTICE 'Completed month %: % units, $% total value (locked: %)',
      month_record.month_date, running_units, running_value, existing_locked;
  END LOOP;

  -- Return summary results
  RETURN QUERY SELECT
    processed_months,
    locked_months,
    total_all_records,
    (now() - start_time)::interval,
    result_details;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION fn_generate_historical_fba_inventory_values() TO service_role;
GRANT EXECUTE ON FUNCTION fn_generate_historical_fba_inventory_values() TO authenticated;
