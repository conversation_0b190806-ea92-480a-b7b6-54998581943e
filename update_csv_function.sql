-- Update CSV export function to match working format (no quotes around fields)

CREATE OR REPLACE FUNCTION public.f_export_tu_informed_to_csv_informed_format()
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    csv_content TEXT;
BEGIN
    -- Generate CSV content matching the exact working format (no quotes around fields)
    WITH csv_rows AS (
        SELECT
            'SKU,MARKETPLACE_ID,COST,CURRENCY,MIN_PRICE,MAX_PRICE,MANAGED,MAP_PRICE,LISTING_TYPE,STRATEGY_ID,DateAdded' as csv_line,
            0 as sort_order

        UNION ALL

        SELECT
            sku || ',' ||
            marketplace_id || ',' ||
            cost || ',' ||
            currency || ',' ||
            min_price || ',' ||
            max_price || ',' ||
            managed || ',' ||
            COALESCE(map_price::text, '0') || ',' ||
            listing_type || ',' ||
            strategy_id || ',' ||
            to_char(dateadded AT TIME ZONE 'UTC', 'M/D/YYYY HH24:MI:SS') as csv_line,
            row_number() OVER (ORDER BY sku) as sort_order
        FROM tu_informed
    )
    SELECT string_agg(csv_line, E'\n' ORDER BY sort_order)
    INTO csv_content
    FROM csv_rows;

    -- Check if CSV content is empty
    IF csv_content IS NULL OR csv_content = '' THEN
        RAISE EXCEPTION 'No data found in tu_informed';
    END IF;

    -- Return the CSV content
    RETURN csv_content;
END;
$$;
