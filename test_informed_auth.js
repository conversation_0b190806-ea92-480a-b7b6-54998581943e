/**
 * Test Informed API authentication and available endpoints
 */

import fetch from 'node-fetch';

const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const BASE_URL = 'https://api.informed.co';

async function testEndpoint(endpoint, method = 'GET', body = null) {
    console.log(`\n=== Testing ${method} ${endpoint} ===`);
    
    try {
        const options = {
            method,
            headers: {
                'x-api-key': API_KEY,
                'Accept': 'application/json'
            }
        };
        
        if (body) {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(body);
        }
        
        const response = await fetch(`${BASE_URL}${endpoint}`, options);
        
        console.log(`Status: ${response.status} ${response.statusText}`);
        
        const responseText = await response.text();
        console.log(`Response: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`);
        
        return response.status;
        
    } catch (error) {
        console.log(`Error: ${error.message}`);
        return null;
    }
}

async function testAuthentication() {
    console.log('Testing Informed API authentication and endpoints...');
    console.log(`Using API key: ${API_KEY.substring(0, 10)}...`);
    
    // Test known working endpoints first
    const endpoints = [
        '/reports/requestReport?reportType=All_Fields',
        '/reports/requests',
        '/v1/feed',
        '/feed',
        '/upload',
        '/pricing/upload',
        '/data/upload',
        '/inventory/upload'
    ];
    
    for (const endpoint of endpoints) {
        await testEndpoint(endpoint);
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Test POST to /v1/feed with different content types
    console.log('\n=== Testing POST to /v1/feed with JSON ===');
    try {
        const response = await fetch(`${BASE_URL}/v1/feed`, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: 'sku,marketplace_id,cost\nTEST,9432,10.00',
                format: 'csv'
            })
        });
        
        console.log(`Status: ${response.status} ${response.statusText}`);
        const responseText = await response.text();
        console.log(`Response: ${responseText}`);
        
    } catch (error) {
        console.log(`Error: ${error.message}`);
    }
    
    // Test if we can get any documentation
    console.log('\n=== Testing for API documentation ===');
    const docEndpoints = ['/', '/docs', '/api', '/help', '/swagger'];
    
    for (const endpoint of docEndpoints) {
        const status = await testEndpoint(endpoint);
        if (status && status !== 404) {
            console.log(`Found potential documentation at: ${endpoint}`);
        }
    }
}

testAuthentication().catch(console.error);
