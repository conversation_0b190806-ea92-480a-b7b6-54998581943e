/**
 * Process a directory to rename image files
 * @param {string} sourceDir - The directory to process
 * @param {string} prefix - The prefix to use for renamed files
 * @param {Object} config - Configuration options
 * @param {boolean} shouldBackup - Whether to back up files from this directory
 * @returns {Promise<Object>} - Result object with renamed files and counts
 */
async function processDirectory(sourceDir, prefix, config, shouldBackup) {
  console.log(`Processing directory: ${sourceDir}`);
  
  const result = {
    renamedCount: 0,
    renamedFiles: [],
    firstRenamedFile: null,
    lastRenamedFile: null
  };
  
  try {
    // Get all files in the directory
    const files = await fs.readdir(sourceDir);
    console.log(`Found ${files.length} files in directory ${sourceDir}`);
    
    // Check for existing backup directories
    const backupDirs = files.filter(file =>
      file.startsWith(config.BACKUP_PREFIX) && existsSync(path.join(sourceDir, file))
    );
    console.log(`Found ${backupDirs.length} existing backup directories in ${sourceDir}`);
    
    // Filter for image files only (excluding backup directories)
    let imageFiles = files.filter(file => {
      // Skip directories, especially backup directories
      if (existsSync(path.join(sourceDir, file)) &&
          (file.startsWith(config.BACKUP_PREFIX) || !path.extname(file))) {
        return false;
      }
      const ext = path.extname(file).toLowerCase();
      return config.EXTENSIONS.includes(ext);
    });
    console.log(`Found ${imageFiles.length} image files in ${sourceDir}`);
    
    // Get file stats for sorting
    const fileDetails = [];
    for (const file of imageFiles) {
      const filePath = path.join(sourceDir, file);
      const stats = await fs.stat(filePath);
      
      fileDetails.push({
        name: file,
        path: filePath,
        created: stats.birthtime,
        modified: stats.mtime
      });
    }
    
    // Sort files based on the selected method
    if (config.SORT_BY === 'created') {
      console.log('Sorting by file creation date...');
      fileDetails.sort((a, b) => a.created - b.created);
    } else if (config.SORT_BY === 'modified') {
      console.log('Sorting by file modification date...');
      fileDetails.sort((a, b) => a.modified - b.modified);
    } else {
      console.log('Sorting alphabetically by filename...');
      fileDetails.sort((a, b) => a.name.localeCompare(b.name));
    }
    
    // Update imageFiles array with the sorted order
    imageFiles = fileDetails.map(detail => detail.name);
    
    // Preview the renaming operations
    console.log('\nRenaming Preview for directory:', sourceDir);
    console.log('------------------');
    
    const renameOperations = [];
    let changesNeeded = false;
    
    for (let i = 0; i < imageFiles.length; i++) {
      const oldFile = imageFiles[i];
      const oldPath = path.join(sourceDir, oldFile);
      
      // Get file extension
      const ext = path.extname(oldFile).toLowerCase();
      
      // Create new filename with padded index
      const index = config.START_INDEX + i;
      const paddedIndex = index.toString().padStart(config.PADDING, '0');
      const newFile = `${prefix}-${paddedIndex}${ext}`;
      const newPath = path.join(sourceDir, newFile);
      
      // Only rename if the file isn't already correctly named
      if (oldFile !== newFile) {
        console.log(`${oldFile} -> ${newFile}`);
        renameOperations.push({ oldPath, newPath });
        changesNeeded = true;
      } else {
        console.log(`${oldFile} (already correctly named)`);
      }
    }
    
    if (!changesNeeded) {
      console.log(`\nAll files in ${sourceDir} are already correctly named. No changes needed.`);
      return result;
    }
    
    // When called from the task queue, we always execute the rename
    const executeRename = config.EXECUTE_RENAME;
    
    if (executeRename) {
      console.log(`\nExecuting rename operations in ${sourceDir}...`);
      
      // Check if we need to create a backup
      let backupDir;
      if (shouldBackup && backupDirs.length === 0) {
        // Create a backup directory only if one doesn't already exist
        backupDir = path.join(sourceDir, config.BACKUP_PREFIX + Date.now());
        await fs.mkdir(backupDir);
        console.log(`Created backup directory: ${backupDir}`);
        
        // Copy files to backup directory first
        for (const { oldPath } of renameOperations) {
          const fileName = path.basename(oldPath);
          const backupPath = path.join(backupDir, fileName);
          await fs.copyFile(oldPath, backupPath);
        }
        console.log('Backup completed');
      } else if (shouldBackup && backupDirs.length > 0) {
        console.log(`Using existing backup directory: ${backupDirs[0]}`);
      } else {
        console.log('Skipping backup for this directory');
      }
      
      // Perform the renaming
      let successCount = 0;
      let errorCount = 0;
      
      // First, check for potential conflicts (files that would be overwritten)
      const tempSuffix = '.temp_' + Date.now();
      const conflictResolutionNeeded = new Set();
      
      // First pass: rename files that would be overwritten to temporary names
      for (const { oldPath, newPath } of renameOperations) {
        if (existsSync(newPath) && oldPath !== newPath) {
          const tempPath = newPath + tempSuffix;
          try {
            await fs.rename(newPath, tempPath);
            conflictResolutionNeeded.add(newPath);
            console.log(`Temporarily renamed ${newPath} to ${tempPath} to avoid conflict`);
          } catch (error) {
            console.error(`Error handling conflict for ${newPath}: ${error.message}`);
            errorCount++;
          }
        }
      }
      
      // Second pass: perform the actual renaming
      for (const { oldPath, newPath } of renameOperations) {
        try {
          // Skip if source and destination are the same
          if (oldPath === newPath) {
            console.log(`Skipping ${oldPath} (already correctly named)`);
            continue;
          }
          
          await fs.rename(oldPath, newPath);
          successCount++;
          
          // Track the first and last renamed files
          const newFileName = path.basename(newPath);
          if (result.firstRenamedFile === null) {
            result.firstRenamedFile = newFileName;
          }
          result.lastRenamedFile = newFileName;
          
          // Add to the list of renamed files
          result.renamedFiles.push({ name: newFileName, path: newPath });
        } catch (error) {
          console.error(`Error renaming ${oldPath}: ${error.message}`);
          errorCount++;
        }
      }
      
      // Third pass: handle any temporary files that need to be restored
      for (const conflictPath of conflictResolutionNeeded) {
        const tempPath = conflictPath + tempSuffix;
        if (existsSync(tempPath)) {
          try {
            // Find a new name for this file
            let counter = 1;
            let newPath;
            do {
              newPath = `${conflictPath}.conflict_${counter++}`;
            } while (existsSync(newPath));
            
            await fs.rename(tempPath, newPath);
            console.log(`Renamed conflict file to ${newPath}`);
          } catch (error) {
            console.error(`Error resolving conflict for ${tempPath}: ${error.message}`);
            errorCount++;
          }
        }
      }
      
      console.log(`\nRenaming completed in ${sourceDir}: ${successCount} successful, ${errorCount} failed`);
      result.renamedCount = successCount;
      
      // Log the first and last renamed files
      if (successCount > 0 && result.firstRenamedFile && result.lastRenamedFile) {
        console.log(`First renamed file in ${sourceDir}: ${result.firstRenamedFile}`);
        console.log(`Last renamed file in ${sourceDir}: ${result.lastRenamedFile}`);
      }
    } else {
      console.log(`\nRename operation was not executed in ${sourceDir}. Set EXECUTE_RENAME = true to perform the actual renaming.`);
    }
  } catch (error) {
    console.error(`Error processing directory ${sourceDir}:`, error.message);
  }
  
  return result;
}
