-- Check the size of v_informed_upload view
-- Run this to see how much data we're dealing with

-- Get the count of records
SELECT COUNT(*) as record_count FROM v_informed_upload;

-- Get a sample of the first 5 records to see the structure
SELECT * FROM v_informed_upload LIMIT 5;

-- Check if there are any obvious performance issues
-- (This might help identify if the view query itself is the problem)
EXPLAIN (ANALYZE, BUFFERS) SELECT COUNT(*) FROM v_informed_upload;
