// enqueueRenameAndUploadImagesTask.js - <PERSON><PERSON><PERSON> to enqueue a rename_and_upload_images task
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Enqueues a rename_and_upload_images task
 * @param {string} folderId - The folder ID to process
 * @returns {Promise<Object>} The created task data
 */
async function enqueueRenameAndUploadImagesTask(folderId) {
  try {
    console.log(`[enqueueRenameAndUploadImagesTask.js] Enqueueing rename_and_upload_images task for folder ${folderId}`);
    
    const now = new Date();
    // Schedule the task 5 minutes in the future
    const scheduledAt = new Date(now.getTime() + 5 * 60 * 1000);
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'rename_and_upload_images',
        status: 'pending',
        payload: { folderId },
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString()
      })
      .select();
    
    if (error) {
      console.error(`[enqueueRenameAndUploadImagesTask.js] Error enqueueing task: ${error.message}`);
      return null;
    }
    
    console.log(`[enqueueRenameAndUploadImagesTask.js] Successfully enqueued task with ID ${data[0].id}`);
    return data[0];
  } catch (err) {
    console.error(`[enqueueRenameAndUploadImagesTask.js] Exception while enqueueing task: ${err.message}`);
    return null;
  }
}

// If this script is run directly (not imported), execute the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  // Check for folder ID argument
  const args = process.argv.slice(2);
  const folderIdArg = args.find(arg => arg.startsWith('--folder='));
  
  if (folderIdArg) {
    const folderId = folderIdArg.split('=')[1];
    console.log(`Running script for folder ID: ${folderId}`);
    
    enqueueRenameAndUploadImagesTask(folderId)
      .then(task => {
        console.log('Task enqueued:', task);
        process.exit(0);
      })
      .catch(err => {
        console.error('Error:', err);
        process.exit(1);
      });
  } else {
    console.error('Error: No folder ID specified. Use --folder=XXXXX argument.');
    process.exit(1);
  }
}
