import { exportOnly } from './informedUploadHandler.js';

async function checkStrategyIds() {
    try {
        const result = await exportOnly();
        if (result.success) {
            const lines = result.csvContent.split('\n');
            console.log('=== CSV HEADER ===');
            console.log(lines[0]);
            console.log('=== FIRST 5 DATA ROWS ===');
            for (let i = 1; i <= 5; i++) {
                console.log(`Line ${i+1}: ${lines[i]}`);
            }
            
            // Extract unique strategy IDs
            const strategyIds = new Set();
            for (let i = 1; i < lines.length && i <= 100; i++) {
                if (lines[i]) {
                    const fields = lines[i].split(',');
                    const strategyId = fields[9]; // STRATEGY_ID is the 10th field (index 9)
                    strategyIds.add(strategyId);
                }
            }
            console.log('=== UNIQUE STRATEGY_IDs IN FIRST 100 ROWS ===');
            console.log(Array.from(strategyIds));
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

checkStrategyIds();
