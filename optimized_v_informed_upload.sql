-- Optimized version of v_informed_upload and supporting views
-- This approach reduces subqueries and improves performance significantly

-- Step 1: Create a materialized view for config values (these rarely change)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_config_cache AS
SELECT 
    MAX(CASE WHEN key = 'fba_min_profit' THEN value::numeric END) as fba_min_profit,
    MAX(CASE WHEN key = 'fba_s_and_h_fees' THEN value::numeric END) as fba_s_and_h_fees,
    MAX(CASE WHEN key = 'fbm_fees' THEN value::numeric END) as fbm_fees
FROM t_config 
WHERE key IN ('fba_min_profit', 'fba_s_and_h_fees', 'fbm_fees');

-- Create index for faster access
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_config_cache_single_row ON mv_config_cache ((1));

-- Step 2: Create a materialized view for disc counts (expensive aggregations)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_sdasin_disc_counts AS
SELECT 
    tj.sdasin_id,
    COUNT(*) FILTER (WHERE td.sold_date IS NULL) as disc_qty_in_stock,
    COUNT(*) FILTER (WHERE td.sold_date IS NOT NULL AND td.sold_channel = 'FBA') as disc_qty_sold_fba,
    COUNT(*) FILTER (WHERE td.sold_date IS NOT NULL AND td.sold_channel = 'FBM') as disc_qty_sold_fbm
FROM tjoin_discs_sdasins tj
JOIN t_discs td ON tj.disc_id = td.id
GROUP BY tj.sdasin_id;

-- Create index for faster lookups
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_sdasin_disc_counts_sdasin_id ON mv_sdasin_disc_counts (sdasin_id);

-- Step 3: Create optimized base view for FBA calculations
CREATE OR REPLACE VIEW v_informed_prep_discs_fba_optimized AS
WITH config AS (
    SELECT * FROM mv_config_cache LIMIT 1
),
sdasin_data AS (
    SELECT 
        ts.id as sdasin_id,
        ts.fba_sku as sku,
        ts.override_amazon_max_price,
        COALESCE(vsa.avg_carrying_cost_fba, 0.0) as avg_carrying_cost,
        tm.val_override_map_price,
        tm.val_override_max_amazon_price,
        tp.val_map_price,
        tp.val_max_amazon_price,
        tp.val_msrp,
        vs.fba_informed_strategy_id
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    WHERE ts.fba_uploaded_at IS NOT NULL
)
SELECT 
    sd.sku,
    COALESCE(mdc.disc_qty_in_stock, 0) as disc_qty_in_stock,
    COALESCE(mdc.disc_qty_sold_fba, 0) as disc_qty_sold_fba,
    -- Pre-calculate base costs
    (sd.avg_carrying_cost + c.fba_min_profit + c.fba_s_and_h_fees) as total_cost,
    (sd.avg_carrying_cost + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85 as total_cost_with_fees,
    -- Calculate pricing
    GREATEST(
        COALESCE(sd.val_override_map_price, sd.val_map_price, 0)::double precision,
        (sd.avg_carrying_cost + c.fba_min_profit + c.fba_s_and_h_fees) / 0.85
    ) as our_min,
    COALESCE(
        sd.override_amazon_max_price,
        sd.val_override_max_amazon_price,
        sd.val_max_amazon_price,
        sd.val_msrp
    ) as our_max,
    COALESCE(sd.val_override_map_price, sd.val_map_price) as our_map,
    sd.fba_informed_strategy_id as informed_strategy_id,
    'Amazon FBA' as listing_type
FROM sdasin_data sd
CROSS JOIN config c
LEFT JOIN mv_sdasin_disc_counts mdc ON sd.sdasin_id = mdc.sdasin_id;

-- Step 4: Create optimized base view for FBM calculations
CREATE OR REPLACE VIEW v_informed_prep_discs_fbm_optimized AS
WITH config AS (
    SELECT * FROM mv_config_cache LIMIT 1
),
sdasin_data AS (
    SELECT 
        ts.id as sdasin_id,
        ts.fbm_sku as sku,
        ts.override_amazon_max_price,
        COALESCE(vsa.avg_carrying_cost_fbm, 0.0) as avg_carrying_cost,
        tm.val_override_map_price,
        tm.val_override_max_amazon_price,
        tp.val_map_price,
        tp.val_max_amazon_price,
        tp.val_msrp,
        vs.fbm_informed_strategy_id
    FROM t_sdasins ts
    JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
    JOIN t_mps tm ON ts.mps_id = tm.id
    JOIN t_plastics tp ON tm.plastic_id = tp.id
    LEFT JOIN v_sdasins vs ON ts.id = vs.id
    WHERE ts.fbm_uploaded_at IS NOT NULL
)
SELECT 
    sd.sku,
    COALESCE(mdc.disc_qty_in_stock, 0) as disc_qty_in_stock,
    COALESCE(mdc.disc_qty_sold_fbm, 0) as disc_qty_sold_fbm,
    -- Pre-calculate base costs
    (sd.avg_carrying_cost + c.fbm_fees) as total_cost,
    (sd.avg_carrying_cost + c.fbm_fees) / 0.85 as total_cost_with_fees,
    -- Calculate pricing
    GREATEST(
        COALESCE(sd.val_override_map_price, sd.val_map_price, 0)::double precision,
        (sd.avg_carrying_cost + c.fbm_fees) / 0.85
    ) as our_min,
    COALESCE(
        sd.override_amazon_max_price,
        sd.val_override_max_amazon_price,
        sd.val_max_amazon_price,
        sd.val_msrp
    ) as our_max,
    COALESCE(sd.val_override_map_price, sd.val_map_price) as our_map,
    sd.fbm_informed_strategy_id as informed_strategy_id,
    'Amazon FBM' as listing_type
FROM sdasin_data sd
CROSS JOIN config c
LEFT JOIN mv_sdasin_disc_counts mdc ON sd.sdasin_id = mdc.sdasin_id;

-- Step 5: Create optimized main view
CREATE OR REPLACE VIEW v_informed_upload_optimized AS
SELECT
    vpfba.sku,
    '9432'::text as marketplace_id,
    (vpfba.our_min - 0.01)::numeric as cost,
    'USD'::text as currency,
    vpfba.our_min as min_price,
    vpfba.our_max as max_price,
    vpfba.our_map as map_price,
    vpfba.listing_type,
    vs.fba_informed_strategy_id as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM v_informed_prep_discs_fba_optimized vpfba
JOIN v_informed_prep_disctint_fba_sdasins vs ON vpfba.sku = vs.fba_sku

UNION ALL

SELECT
    vpfbm.sku,
    '9432'::text as marketplace_id,
    (vpfbm.our_min - 0.01)::numeric as cost,
    'USD'::text as currency,
    vpfbm.our_min as min_price,
    vpfbm.our_max as max_price,
    vpfbm.our_map as map_price,
    vpfbm.listing_type,
    vs.fbm_informed_strategy_id as strategy_id,
    '1'::text as managed,
    now() as dateadded
FROM v_informed_prep_discs_fbm_optimized vpfbm
JOIN v_informed_prep_disctint_fbm_sdasins vs ON vpfbm.sku = vs.fbm_sku;

-- Step 6: Create functions to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_informed_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW mv_config_cache;
    REFRESH MATERIALIZED VIEW mv_sdasin_disc_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Create a function to get count from optimized view
CREATE OR REPLACE FUNCTION fn_count_v_informed_upload_optimized()
RETURNS integer AS $$
DECLARE
    record_count integer;
BEGIN
    SELECT COUNT(*) INTO record_count FROM v_informed_upload_optimized;
    RETURN record_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN -1; -- Return -1 to indicate error
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
