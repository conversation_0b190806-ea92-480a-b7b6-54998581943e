-- Update Informed Repricer tables to include all columns from the CSV files

-- Add missing columns to it_infor_all_fields table
ALTER TABLE public.it_infor_all_fields 
ADD COLUMN IF NOT EXISTS "SALES_RANK_CATEGORY" text NULL;

-- You can add more columns here as they are discovered
-- ALTER TABLE public.it_infor_all_fields 
-- ADD COLUMN IF NOT EXISTS "ANOTHER_COLUMN" text NULL;

-- Update the other tables if needed
-- ALTER TABLE public.it_infor_competition_landscape 
-- ADD COLUMN IF NOT EXISTS "MISSING_COLUMN" text NULL;

-- ALTER TABLE public.it_infor_no_buy_box 
-- ADD COLUMN IF NOT EXISTS "MISSING_COLUMN" text NULL;
