// Add this JavaScript to handle the Amazon FBA import button click
document.addEventListener('DOMContentLoaded', function() {
  // Amazon FBA Import
  const runAmazonImportBtn = document.getElementById('run-amazon-import');
  const amazonImportResult = document.getElementById('amazon-import-result');
  
  if (runAmazonImportBtn) {
    runAmazonImportBtn.addEventListener('click', function() {
      // Disable button and show loading
      runAmazonImportBtn.disabled = true;
      runAmazonImportBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Running import...';
      amazonImportResult.innerHTML = '<div class="alert alert-info">Import in progress. This may take a few minutes...</div>';
      
      // Call the API endpoint
      fetch('/api/import-amazon-fba', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        // Re-enable button
        runAmazonImportBtn.disabled = false;
        runAmazonImportBtn.innerHTML = 'Run Amazon FBA Import';
        
        if (data.success) {
          amazonImportResult.innerHTML = `
            <div class="alert alert-success">
              <h4>Import Successful!</h4>
              <p>Imported ${data.importCount} records into the database.</p>
              <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details}</pre>
            </div>
          `;
        } else {
          amazonImportResult.innerHTML = `
            <div class="alert alert-danger">
              <h4>Import Failed</h4>
              <p>${data.error}</p>
              <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
            </div>
          `;
        }
      })
      .catch(error => {
        // Re-enable button
        runAmazonImportBtn.disabled = false;
        runAmazonImportBtn.innerHTML = 'Run Amazon FBA Import';
        
        amazonImportResult.innerHTML = `
          <div class="alert alert-danger">
            <h4>Import Failed</h4>
            <p>${error.message}</p>
          </div>
        `;
      });
    });
  }
});