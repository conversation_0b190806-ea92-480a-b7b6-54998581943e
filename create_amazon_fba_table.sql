-- Create table for Amazon FBA inventory reports
CREATE TABLE IF NOT EXISTS public.it_amazon_fba_inventory (
    id SERIAL PRIMARY KEY,
    "Date" TEXT,
    "FNSKU" TEXT,
    "ASIN" TEXT,
    "MSKU" TEXT,
    "Title" TEXT,
    "Disposition" TEXT,
    "Starting_Warehouse_Balance" INTEGER,
    "In_Transit_Between_Warehouses" INTEGER,
    "Receipts" INTEGER,
    "Customer_Shipments" INTEGER,
    "Customer_Returns" INTEGER,
    "Vendor_Returns" INTEGER,
    "Warehouse_Transfer_In_Out" INTEGER,
    "Found" INTEGER,
    "Lost" INTEGER,
    "Damaged" INTEGER,
    "Disposed" INTEGER,
    "Other_Events" INTEGER,
    "Ending_Warehouse_Balance" INTEGER,
    "Unknown_Events" INTEGER,
    "Location" TEXT,
    "Store" TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    import_batch_id UUID DEFAULT gen_random_uuid()
);

-- Create index on common search fields
CREATE INDEX IF NOT EXISTS idx_amazon_fba_asin ON public.it_amazon_fba_inventory("ASIN");
CREATE INDEX IF NOT EXISTS idx_amazon_fba_msku ON public.it_amazon_fba_inventory("MSKU");
CREATE INDEX IF NOT EXISTS idx_amazon_fba_fnsku ON public.it_amazon_fba_inventory("FNSKU");
CREATE INDEX IF NOT EXISTS idx_amazon_fba_location ON public.it_amazon_fba_inventory("Location");
CREATE INDEX IF NOT EXISTS idx_amazon_fba_date ON public.it_amazon_fba_inventory("Date");
CREATE INDEX IF NOT EXISTS idx_amazon_fba_import_batch ON public.it_amazon_fba_inventory(import_batch_id);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp
BEFORE UPDATE ON public.it_amazon_fba_inventory
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add comment
COMMENT ON TABLE public.it_amazon_fba_inventory IS 'Historical Amazon FBA inventory data from monthly reports';