# Informed Repricer Integration

This integration allows you to download and import reports from the Informed Repricer API into your Supabase database.

## Table Structures

The integration uses the following tables:

1. `it_infor_all_fields`: Contains all fields from the Informed Repricer API
   - Primary key: `SKU`
   - Contains pricing, inventory, and strategy information

2. `it_infor_competition_landscape`: Contains competition landscape data
   - Primary key: `SKU`
   - Contains competitor pricing and strategy information

3. `it_infor_no_buy_box`: Contains data for listings without a buy box
   - Primary key: `SKU`
   - Contains pricing and strategy information

## Setup

1. Create the necessary tables in your Supabase database:
   ```
   psql -U your_username -d your_database -f create_informed_tables.sql
   ```

2. Create the SQL functions for the task queue integration:
   ```
   psql -U your_username -d your_database -f fn_truncate_table.sql
   psql -U your_username -d your_database -f fn_enqueue_informed_tasks.sql
   psql -U your_username -d your_database -f fn_create_informed_scheduler_trigger.sql
   ```

## Usage

### Automatic Scheduling

The integration is designed to run automatically at 6:30 AM, 12:30 PM, and 6:30 PM CST every day. The scheduler will enqueue a task to download and import the reports.

### Manual Download

You can manually download the reports using the admin interface:

1. Open the admin interface at http://localhost:3001/admin.html
2. Go to the "Informed" tab
3. Click the "Download Reports" button

### Automated Import

The integration now uses a direct Node.js importer to import the data, which allows it to work with the admin interface and scheduling:

1. Download the reports using the admin interface
2. Click the "Import Reports" button in the admin interface

The import process will:
1. Parse the CSV files using Node.js
2. Connect directly to Supabase using the JavaScript client
3. Import the data in batches for better performance
4. Update the timestamps and log the import

### Manual Import

If you need to manually import the data:

1. Download the reports using the admin interface
2. Run the Node.js importer directly:

   ```
   node informedDirectImporter.js
   ```

   > **Note:** The importer uses the environment variables from your `.env` file for the Supabase connection details.

## Troubleshooting

### Download Issues

If you encounter issues with downloading the reports:

1. Check the API key in the `informedReportDownloader.cjs` file
2. Check the network connectivity to the Informed Repricer API
3. Check the logs in the admin interface

### Import Issues

If you encounter issues with importing the reports:

1. Check the Supabase connection in the `.env` file
2. Try manually importing the reports using the SQL script
3. Check the logs in the admin interface

## Files

### JavaScript Files
- `informedReportDownloader.cjs`: Downloads reports from the Informed Repricer API
- `informedDirectImporter.js`: Imports the downloaded reports directly into Supabase
- `informedApiHandler.js`: Handles the API endpoints for the admin interface

### SQL Files
- `create_informed_tables.sql`: Creates the necessary tables in Supabase
- `fn_update_timestamp.sql`: Creates a function to update timestamps
- `fn_enqueue_informed_tasks.sql`: Creates a function to enqueue tasks
- `fn_create_informed_scheduler_trigger.sql`: Creates a scheduler trigger

## API Endpoints

- `GET /api/informed/download`: Downloads the reports
- `GET /api/informed/import`: Imports the downloaded reports
- `GET /api/informed/status`: Gets the status of the integration
