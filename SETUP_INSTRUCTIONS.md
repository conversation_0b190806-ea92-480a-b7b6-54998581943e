# Fix Hardcoded Pricing in Informed Repricer

## Problem
Your Informed Repricer is using hardcoded values ($15 min, $50 max, $20 MAP) instead of real pricing from your MPS and plastics data.

## Solution
Run these SQL scripts in your Supabase SQL Editor to set up the optimized system with real calculations.

## Step 1: Run setup_optimized_informed_system.sql
1. Go to your Supabase dashboard
2. Click "SQL Editor" 
3. Create a new query
4. Copy and paste the entire contents of `setup_optimized_informed_system.sql`
5. Click "Run"

This creates:
- Materialized views for performance (mv_config_cache, mv_sdasin_disc_counts)
- Optimized prep views for FBA and FBM calculations
- Main optimized view (v_informed_upload_optimized)

## Step 2: Run setup_optimized_functions.sql
1. In the same SQL Editor
2. Create another new query
3. Copy and paste the entire contents of `setup_optimized_functions.sql`
4. Click "Run"

This creates:
- Functions to refresh materialized views
- Function to count optimized records
- Main function: fn_fill_tu_informed_optimized (uses REAL pricing!)

## Step 3: Test the Setup
After running both scripts, test the Informed workflow:

1. Go to admin interface: http://localhost:3002/admin.html
2. Click "Informed" tab
3. Click "Fill tu_informed" 
4. You should see: "✅ Successfully filled tu_informed using optimized approach"
5. Check your tu_informed table - prices should now be real, not hardcoded!

## What This Fixes
- **Before**: min_price: $15, max_price: $50, map_price: $20 (hardcoded)
- **After**: Real calculations from t_mps.val_override_map_price, t_plastics.val_map_price, etc.

## Real Pricing Logic
- **MAP Price**: COALESCE(mps_override_map_price, plastic_map_price)
- **Min Price**: GREATEST(MAP_price, calculated_cost_with_fees)  
- **Max Price**: COALESCE(sdasin_override, mps_override, plastic_max, plastic_msrp)

The system will now use your actual product data instead of hardcoded fallback values!
