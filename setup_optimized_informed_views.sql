-- Setup script for optimized Informed views
-- Run this script in your Supabase SQL editor to set up the optimized views

-- Step 1: Create the optimized views and functions
\i optimized_v_informed_upload.sql

-- Step 2: Create the optimized fill function
\i fn_fill_tu_informed_optimized.sql

-- Step 3: Initial refresh of materialized views
SELECT refresh_informed_materialized_views();

-- Step 4: Test the optimized count function
SELECT fn_count_v_informed_upload_optimized() as record_count;

-- Step 5: Show some sample data from the optimized view
SELECT * FROM v_informed_upload_optimized LIMIT 5;

-- Step 6: Compare performance (optional)
-- You can run these to see the difference in execution time:
-- EXPLAIN (ANALYZE, BUFFERS) SELECT COUNT(*) FROM v_informed_upload;
-- EXPLAIN (ANALYZE, BUFFERS) SELECT COUNT(*) FROM v_informed_upload_optimized;
