-- Create tables for Informed Repricer integration

-- Create the All Fields table
CREATE TABLE IF NOT EXISTS public.it_infor_all_fields (
  "SKU" text not null,
  "STOCK" integer null,
  "ITEM_ID" text null,
  "<PERSON><PERSON>" text null,
  "<PERSON>IT<PERSON>" text null,
  "MARKETPLACE_ID" integer null,
  "COST" numeric null,
  "CURRENCY" text null,
  "CURRENT_VELOCITY" smallint null,
  "MIN_PRICE" numeric null,
  "CALC_MIN_PRICE" numeric null,
  "MAX_PRICE" numeric null,
  "CALC_MAX_PRICE" numeric null,
  "CURRENT_PRICE" numeric null,
  "CURRENT_SHIPPING" numeric null,
  "MANUAL_PRICE" numeric null,
  "OR<PERSON>INAL_PRICE" numeric null,
  "MAP_PRICE" numeric null,
  "LISTING_TYPE" text null,
  "STRATEGY_ID" integer null,
  "BUY_BOX_PRICE" numeric null,
  "BUYBOX_SELLER" text null,
  "BUY<PERSON>X_WINNER" text null,
  "VAT_PERCENTAGE" text null,
  "DATE_ADDED" timestamp without time zone null,
  "STOCK_COST_VALUE" numeric null,
  "DAYS_SINCE_BUYBOX" smallint null,
  "DATE_OF_LAST_SALE" timestamp without time zone null,
  "DAYS_REMAINING" integer null,
  "AVAILABILITY" text null,
  "OLDEST_STOCK_DATE" timestamp without time zone null,
  created_at timestamp with time zone null default now(),
  constraint it_informed_all_fields_pkey primary key ("SKU")
);

-- Create the Competition Landscape table
CREATE TABLE IF NOT EXISTS public.it_infor_competition_landscape (
  "SKU" text not null,
  "ITEM_ID" text null,
  "STOCK" integer null,
  "MARKETPLACE_ID" integer null,
  "COST" numeric null,
  "CURRENCY" text null,
  "MIN_PRICE" numeric null,
  "CALC_MIN_PRICE" numeric null,
  "MAX_PRICE" numeric null,
  "CALC_MAX_PRICE" numeric null,
  "CURRENT_PRICE" numeric null,
  "COMP_PRICE" numeric null,
  "COMP_ID" text null,
  "COMP_LISTING_TYPE" text null,
  "BUYBOX_PRICE" numeric null,
  "BUYBOX_SELLER" text null,
  "BUYBOX_WINNER" text null,
  "STRATEGY_ID" integer null,
  created_at timestamp with time zone null default now(),
  constraint it_info_competition_landscape_pkey primary key ("SKU")
);

-- Create the No Buy Box table
CREATE TABLE IF NOT EXISTS public.it_infor_no_buy_box (
  "SKU" text not null,
  "ITEM_ID" text null,
  "TITLE" text null,
  "MARKETPLACE_ID" integer null,
  "COST" numeric null,
  "CURRENCY" text null,
  "MIN_PRICE" numeric null,
  "CALC_MIN_PRICE" numeric null,
  "MAX_PRICE" numeric null,
  "CALC_MAX_PRICE" numeric null,
  "CURRENT_PRICE" numeric null,
  "BUY_BOX_PRICE" numeric null,
  "BUY_BOX_WINNER" text null,
  "STRATEGY_ID" integer null,
  "Fulfillment Type" text null,
  "Marketplace ID" integer null,
  created_at timestamp with time zone null default now(),
  constraint it_infor_no_buy_box_pkey primary key ("SKU")
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_it_infor_all_fields_item_id ON public.it_infor_all_fields("ITEM_ID");
CREATE INDEX IF NOT EXISTS idx_it_infor_competition_landscape_item_id ON public.it_infor_competition_landscape("ITEM_ID");
CREATE INDEX IF NOT EXISTS idx_it_infor_no_buy_box_item_id ON public.it_infor_no_buy_box("ITEM_ID");

-- Grant permissions
GRANT ALL ON public.it_infor_all_fields TO postgres;
GRANT ALL ON public.it_infor_competition_landscape TO postgres;
GRANT ALL ON public.it_infor_no_buy_box TO postgres;
