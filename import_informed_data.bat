@echo off
echo Importing Informed Repricer data...
echo.

REM Set the path to your PostgreSQL installation
set PGBIN=C:\Program Files\PostgreSQL\14\bin
set PGUSER=postgres
set PGPASSWORD=your_password
set PGDATABASE=your_database
set PGHOST=localhost

REM Change these paths to match your environment
set SCRIPT_PATH=C:\Users\<USER>\supabase_project\import_informed_data.sql

echo Connecting to PostgreSQL...
echo.

REM Run the import script
"%PGBIN%\psql" -U %PGUSER% -d %PGDATABASE% -h %PGHOST% -f %SCRIPT_PATH%

echo.
echo Import completed.
pause
