-- Fix the CSV export format to match Informed's expected format (final corrected version)

CREATE OR REPLACE FUNCTION public.f_export_tu_informed_to_csv_informed_format()
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    csv_content TEXT;
    header TEXT := '"SKU","MARKETPLACE_ID","COST","CURRENCY","MIN_PRICE","MAX_PRICE","MANAGED","MAP_PRICE","LISTING_TYPE","STRATEGY_ID","DateAdded"';
BEGIN
    -- Generate CSV content with proper format for Informed
    -- Use a subquery to properly aggregate the data
    SELECT header || E'\n' || string_agg(csv_row, E'\n')
    INTO csv_content
    FROM (
        SELECT format('"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"',
            sku, 
            marketplace_id, 
            cost::text, 
            currency, 
            min_price::text, 
            max_price::text, 
            managed,  -- MANAGED comes before MAP_PRICE
            COALESCE(map_price::text, '0'), -- Convert to text and handle NULL values with '0'
            listing_type, 
            strategy_id::text, 
            to_char(dateadded AT TIME ZONE 'UTC', 'MM/DD/YYYY HH24:MI:SS') -- Format date like 3/10/2024 9:48:27
        ) as csv_row
        FROM tu_informed
        ORDER BY sku -- Ensure consistent ordering
    ) as formatted_rows;

    -- Check if CSV content is empty
    IF csv_content IS NULL OR csv_content = '' THEN
        RAISE EXCEPTION 'No data found in tu_informed';
    END IF;

    -- Return the CSV content
    RETURN csv_content;
END;
$$;

-- Test the final corrected function with a small sample
SELECT 
    split_part(f_export_tu_informed_to_csv_informed_format(), E'\n', 1) as header,
    split_part(f_export_tu_informed_to_csv_informed_format(), E'\n', 2) as first_row,
    split_part(f_export_tu_informed_to_csv_informed_format(), E'\n', 3) as second_row;
