/**
 * Informed Upload Handler
 *
 * This module handles the complete workflow for uploading data to Informed:
 * 1. Truncate tu_informed table (safely)
 * 2. Fill tu_informed with data from v_informed_upload
 * 3. Generate CSV export
 * 4. Upload CSV to Informed Repricer
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import FormData from 'form-data';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Informed API configuration
const INFORMED_API_KEY = process.env.INFORMED_API_KEY || 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const INFORMED_BASE_URL = 'https://api.informed.co';
const INFORMED_UPLOAD_URL = `${INFORMED_BASE_URL}/v1/feed`; // Correct endpoint

/**
 * Safely truncate tu_informed table using batch delete
 * @returns {Promise<Object>} - Result of the operation
 */
async function truncateTuInformed() {
    console.log('Truncating tu_informed table');

    try {
        // Try a simple delete first - if table is empty, this will be fast
        console.log('Attempting to delete all records from tu_informed...');

        const { error: deleteError, count } = await supabase
            .from('tu_informed')
            .delete()
            .neq('sku', ''); // Delete all records (sku is never empty)

        if (deleteError) {
            console.warn('Simple delete failed, trying batch approach:', deleteError.message);

            // Fallback to batch deletion
            const { count: totalCount, error: countError } = await supabase
                .from('tu_informed')
                .select('*', { count: 'exact', head: true });

            if (countError) {
                console.error('Error getting count for tu_informed:', countError);
                return {
                    success: false,
                    error: countError.message
                };
            }

            console.log(`tu_informed has ${totalCount} records to delete`);

            if (totalCount === 0) {
                console.log('tu_informed is already empty');
                return {
                    success: true,
                    deletedCount: 0
                };
            }
        } else {
            // Simple delete worked
            const deletedCount = count || 0;
            console.log(`✅ Successfully deleted ${deletedCount} records from tu_informed`);
            return {
                success: true,
                deletedCount
            };
        }

        // Delete records in batches
        const batchSize = 1000;
        let deletedCount = 0;

        while (deletedCount < totalCount) {
            // Get a batch of records to delete (only select sku to minimize data transfer)
            const { data: recordsToDelete, error: selectError } = await supabase
                .from('tu_informed')
                .select('sku')
                .limit(batchSize);

            if (selectError) {
                console.error('Error selecting records from tu_informed:', selectError);
                return {
                    success: false,
                    error: selectError.message
                };
            }

            if (!recordsToDelete || recordsToDelete.length === 0) {
                console.log('No more records to delete from tu_informed');
                break;
            }

            console.log(`Deleting batch of ${recordsToDelete.length} records from tu_informed`);

            // Delete the batch using sku filter
            const skusToDelete = recordsToDelete.map(record => record.sku);
            const { error: deleteError } = await supabase
                .from('tu_informed')
                .delete()
                .in('sku', skusToDelete);

            if (deleteError) {
                console.error('Error deleting batch from tu_informed:', deleteError);
                return {
                    success: false,
                    error: deleteError.message
                };
            }

            deletedCount += recordsToDelete.length;
            console.log(`Deleted ${deletedCount}/${totalCount} records from tu_informed`);

            // Small delay to avoid overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        return {
            success: true,
            deletedCount
        };
    } catch (error) {
        console.error('Error truncating tu_informed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Fill tu_informed with data from v_informed_upload
 * @returns {Promise<Object>} - Result of the operation
 */
async function fillTuInformed() {
    console.log('Filling tu_informed with data from v_informed_upload');

    try {
        // First, try JavaScript approach with REAL pricing calculations
        console.log('🎯 Attempting JavaScript approach with REAL pricing calculations...');

        try {
            // Get ALL SDASIN data with pagination to avoid the 1000 record limit
            console.log('Fetching ALL SDASIN data with real pricing fields...');

            let allSdasinData = [];
            let from = 0;
            const pageSize = 1000;
            let hasMore = true;

            while (hasMore) {
                console.log(`Fetching SDASIN records ${from + 1} to ${from + pageSize}...`);

                const { data: pageData, error: pageError } = await supabase
                    .from('t_sdasins')
                    .select(`
                        id,
                        mps_id,
                        fba_sku,
                        fbm_sku,
                        fba_uploaded_at,
                        fbm_uploaded_at,
                        override_amazon_max_price
                    `)
                    .or('and(fba_uploaded_at.not.is.null,fba_sku.not.is.null),and(fbm_uploaded_at.not.is.null,fbm_sku.not.is.null)')
                    .range(from, from + pageSize - 1);

                if (pageError) {
                    console.warn('Error fetching SDASIN page:', pageError.message);
                    throw pageError;
                }

                if (!pageData || pageData.length === 0) {
                    hasMore = false;
                    break;
                }

                allSdasinData = allSdasinData.concat(pageData);
                console.log(`Fetched ${pageData.length} records, total so far: ${allSdasinData.length}`);

                // If we got less than the page size, we're done
                if (pageData.length < pageSize) {
                    hasMore = false;
                } else {
                    from += pageSize;
                }
            }

            if (!allSdasinData || allSdasinData.length === 0) {
                console.log('No SDASIN data found');
                return { success: true, recordCount: 0 };
            }

            console.log(`✅ Found ${allSdasinData.length} total SDASIN records`);

            // Get unique MPS IDs to fetch pricing data
            const mpsIds = [...new Set(allSdasinData.map(s => s.mps_id))];
            console.log(`Fetching pricing data for ${mpsIds.length} unique MPS records...`);

            // Fetch MPS data in batches to avoid timeouts
            let allMpsData = [];
            const mpsBatchSize = 500;

            for (let i = 0; i < mpsIds.length; i += mpsBatchSize) {
                const mpsBatch = mpsIds.slice(i, i + mpsBatchSize);
                console.log(`Fetching MPS batch ${Math.floor(i/mpsBatchSize) + 1}/${Math.ceil(mpsIds.length/mpsBatchSize)} (${mpsBatch.length} records)...`);

                const { data: mpsBatchData, error: mpsBatchError } = await supabase
                    .from('t_mps')
                    .select(`
                        id,
                        plastic_id,
                        val_override_map_price,
                        val_override_max_amazon_price
                    `)
                    .in('id', mpsBatch);

                if (mpsBatchError) {
                    console.warn('Error fetching MPS batch:', mpsBatchError.message);
                    throw mpsBatchError;
                }

                allMpsData = allMpsData.concat(mpsBatchData || []);
            }

            const mpsData = allMpsData;
            console.log(`✅ Fetched ${mpsData.length} total MPS records`);

            // Get unique plastic IDs
            const plasticIds = [...new Set(mpsData.map(m => m.plastic_id))];
            console.log(`Fetching pricing data for ${plasticIds.length} unique plastic records...`);

            // Fetch plastics data in batches to avoid timeouts
            let allPlasticsData = [];
            const plasticsBatchSize = 500;

            for (let i = 0; i < plasticIds.length; i += plasticsBatchSize) {
                const plasticsBatch = plasticIds.slice(i, i + plasticsBatchSize);
                console.log(`Fetching plastics batch ${Math.floor(i/plasticsBatchSize) + 1}/${Math.ceil(plasticIds.length/plasticsBatchSize)} (${plasticsBatch.length} records)...`);

                const { data: plasticsBatchData, error: plasticsBatchError } = await supabase
                    .from('t_plastics')
                    .select(`
                        id,
                        val_map_price,
                        val_max_amazon_price,
                        val_msrp
                    `)
                    .in('id', plasticsBatch);

                if (plasticsBatchError) {
                    console.warn('Error fetching plastics batch:', plasticsBatchError.message);
                    throw plasticsBatchError;
                }

                allPlasticsData = allPlasticsData.concat(plasticsBatchData || []);
            }

            const plasticsData = allPlasticsData;
            console.log(`✅ Fetched ${plasticsData.length} total plastics records`);

            // Create lookup maps for performance
            const mpsLookup = {};
            mpsData.forEach(mps => {
                mpsLookup[mps.id] = mps;
            });

            const plasticsLookup = {};
            plasticsData.forEach(plastic => {
                plasticsLookup[plastic.id] = plastic;
            });

            console.log('Processing SDASIN records with REAL pricing calculations...');

            // Transform the data with REAL pricing calculations
            const transformedData = [];
            let missingMpsCount = 0;
            let missingPlasticCount = 0;

            for (const sdasin of allSdasinData) {
                const mps = mpsLookup[sdasin.mps_id];
                if (!mps) missingMpsCount++;

                const plastic = mps ? plasticsLookup[mps.plastic_id] : null;
                if (mps && !plastic) missingPlasticCount++;

                // Calculate REAL MAP price (this is the key fix!)
                const realMapPrice = (mps && mps.val_override_map_price) ||
                                    (plastic && plastic.val_map_price) ||
                                    0;

                // Calculate REAL min price
                const realMinPrice = Math.max(realMapPrice, 15.00);

                // Calculate REAL max price
                const realMaxPrice = sdasin.override_amazon_max_price ||
                                   (mps && mps.val_override_max_amazon_price) ||
                                   (plastic && plastic.val_max_amazon_price) ||
                                   (plastic && plastic.val_msrp) ||
                                   50.00;

                // Add FBA record if available
                if (sdasin.fba_sku && sdasin.fba_uploaded_at) {
                    transformedData.push({
                        sku: sdasin.fba_sku,
                        marketplace_id: '9432',
                        cost: realMinPrice - 0.01,
                        currency: 'USD',
                        min_price: realMinPrice,
                        max_price: realMaxPrice,
                        map_price: realMapPrice,  // REAL MAP PRICE!
                        listing_type: 'Amazon FBA',
                        strategy_id: 37123,
                        managed: '1',
                        dateadded: new Date().toISOString()
                    });
                }

                // Add FBM record if available
                if (sdasin.fbm_sku && sdasin.fbm_uploaded_at) {
                    transformedData.push({
                        sku: sdasin.fbm_sku,
                        marketplace_id: '9432',
                        cost: realMinPrice - 0.01,
                        currency: 'USD',
                        min_price: realMinPrice,
                        max_price: realMaxPrice,
                        map_price: realMapPrice,  // REAL MAP PRICE!
                        listing_type: 'Amazon FBM',
                        strategy_id: 45364,
                        managed: '1',
                        dateadded: new Date().toISOString()
                    });
                }
            }

            console.log(`Transformed ${transformedData.length} records with REAL pricing`);
            console.log(`Sample MAP prices: ${transformedData.slice(0, 5).map(r => r.map_price).join(', ')}`);

            // Debug: Show data completeness
            console.log(`Data completeness: ${missingMpsCount} SDASIN records missing MPS data, ${missingPlasticCount} MPS records missing plastic data`);

            // Debug: Check for any $20 MAP prices (should not exist with real pricing)
            const twentyDollarPrices = transformedData.filter(r => r.map_price === 20.00);
            if (twentyDollarPrices.length > 0) {
                console.warn(`⚠️  Found ${twentyDollarPrices.length} records with $20 MAP price - this should not happen with real pricing!`);
                console.warn(`Sample $20 records: ${twentyDollarPrices.slice(0, 3).map(r => r.sku).join(', ')}`);
            } else {
                console.log(`✅ No hardcoded $20 MAP prices found - all prices are real!`);
            }

            // Debug: Show MAP price distribution
            const mapPriceStats = {
                zero: transformedData.filter(r => r.map_price === 0).length,
                nonZero: transformedData.filter(r => r.map_price > 0).length,
                twenty: transformedData.filter(r => r.map_price === 20.00).length
            };
            console.log(`MAP price distribution: ${mapPriceStats.zero} zero, ${mapPriceStats.nonZero} non-zero, ${mapPriceStats.twenty} exactly $20`);

            // Debug: Check for specific missing record
            const disc10002 = transformedData.find(r => r.sku === 'Disc_10002');
            if (disc10002) {
                console.log(`✅ Found Disc_10002 with MAP price: $${disc10002.map_price}`);
            } else {
                console.warn(`⚠️  Disc_10002 not found in transformed data - checking source data...`);
                const sourceDisc10002 = allSdasinData.find(s => s.fba_sku === 'Disc_10002' || s.fbm_sku === 'Disc_10002');
                if (sourceDisc10002) {
                    console.warn(`Found Disc_10002 in source data: FBA=${sourceDisc10002.fba_sku}, FBM=${sourceDisc10002.fbm_sku}, MPS_ID=${sourceDisc10002.mps_id}`);
                } else {
                    console.warn(`Disc_10002 not found in source SDASIN data either`);
                }
            }

            // Insert in batches
            const batchSize = 1000;
            let insertedCount = 0;

            for (let i = 0; i < transformedData.length; i += batchSize) {
                const batch = transformedData.slice(i, i + batchSize);
                const batchNumber = Math.floor(i / batchSize) + 1;
                const totalBatches = Math.ceil(transformedData.length / batchSize);

                console.log(`Inserting batch ${batchNumber}/${totalBatches} (${batch.length} records with REAL pricing)`);

                const { error: insertError } = await supabase
                    .from('tu_informed')
                    .insert(batch);

                if (insertError) {
                    console.error(`Error inserting batch ${batchNumber}:`, insertError);
                    throw insertError;
                }

                insertedCount += batch.length;
            }

            console.log('✅ Successfully filled tu_informed using REAL pricing calculations!');
            console.log('✅ Using REAL MAP prices from t_mps.val_override_map_price and t_plastics.val_map_price');
            console.log('✅ NO MORE HARDCODED VALUES!');
            console.log(`✅ Inserted ${insertedCount} records with real pricing data`);

            return {
                success: true,
                recordCount: insertedCount
            };

        } catch (jsError) {
            console.warn('JavaScript approach failed:', jsError.message);
        }

        // Second, try the optimized approach with real calculations (if simple approach fails)
        console.log('Falling back to optimized approach with real pricing calculations...');

        const { data: optimizedResult, error: optimizedError } = await supabase.rpc('fn_fill_tu_informed_optimized');

        if (!optimizedError && optimizedResult && optimizedResult.length > 0) {
            const result = optimizedResult[0];
            if (result.success) {
                console.log(`✅ Successfully filled tu_informed using optimized approach: ${result.record_count} records`);
                console.log('✅ Using REAL pricing calculations from MPS/plastics data (not hardcoded values)');
                return {
                    success: true,
                    recordCount: result.record_count
                };
            } else {
                console.warn('Optimized function returned error:', result.error_message);
            }
        } else if (optimizedError) {
            console.warn('Optimized approach failed:', optimizedError.message);
        }

        // Second, try the approach with carrying costs (full business logic)
        console.log('Falling back to carrying costs approach...');

        const { data: carryingCostsResult, error: carryingCostsError } = await supabase.rpc('fn_fill_tu_informed_with_carrying_costs');

        if (!carryingCostsError && carryingCostsResult && carryingCostsResult.length > 0) {
            const result = carryingCostsResult[0];
            if (result.success) {
                console.log(`Successfully filled tu_informed with carrying costs: ${result.record_count} records`);
                return {
                    success: true,
                    recordCount: result.record_count
                };
            } else {
                console.warn('Carrying costs function returned error:', result.error_message);
            }
        } else if (carryingCostsError) {
            console.warn('Carrying costs function approach failed:', carryingCostsError.message);
        }

        // Third fallback to minimal approach (HARDCODED VALUES - only as last resort)
        console.log('⚠️  Falling back to minimal approach with HARDCODED VALUES...');
        console.log('⚠️  WARNING: This will use hardcoded prices (min: $15, max: $50, map: $20)');

        const { data: minimalResult, error: minimalError } = await supabase.rpc('fn_fill_tu_informed_minimal');

        if (!minimalError && minimalResult && minimalResult.length > 0) {
            const result = minimalResult[0];
            if (result.success) {
                console.log(`⚠️  Successfully filled tu_informed using minimal approach: ${result.record_count} records`);
                console.log('⚠️  WARNING: Using HARDCODED pricing values as last resort fallback!');
                return {
                    success: true,
                    recordCount: result.record_count
                };
            } else {
                console.warn('Minimal function returned error:', result.error_message);
            }
        } else if (minimalError) {
            console.warn('Minimal function approach failed:', minimalError.message);
        }

        // Fallback: try to get a count to see how much data we're dealing with
        console.log('Falling back to count check...');

        const { data: countResult, error: countError } = await supabase.rpc('fn_count_v_informed_upload_minimal');

        if (!countError && countResult !== null) {
            if (countResult === -1) {
                console.warn('Error counting records in v_informed_upload');
            } else {
                console.log(`Found ${countResult} records in v_informed_upload`);

                // If we have a large dataset, use the chunked approach
                if (countResult > 5000) {
                    console.log('Large dataset detected, using chunked approach...');

                    const { data: chunkedResult, error: chunkedError } = await supabase.rpc('fn_fill_tu_informed_chunked', { chunk_size: 1000 });

                    if (!chunkedError && chunkedResult && chunkedResult.length > 0) {
                        const result = chunkedResult[0];
                        if (result.success) {
                            console.log(`Successfully filled tu_informed using chunked approach: ${result.total_records} records in ${result.chunks_processed} chunks`);
                            return {
                                success: true,
                                recordCount: result.total_records
                            };
                        } else {
                            console.warn('Chunked function returned error:', result.error_message);
                        }
                    } else if (chunkedError) {
                        console.warn('Chunked function approach failed:', chunkedError.message);
                    }
                } else {
                    console.log('Small dataset detected, using standard SQL function...');

                    const { data: sqlResult, error: sqlError } = await supabase.rpc('fn_fill_tu_informed');

                    if (!sqlError && sqlResult && sqlResult.length > 0) {
                        const result = sqlResult[0];
                        if (result.success) {
                            console.log(`Successfully filled tu_informed using SQL function: ${result.record_count} records`);
                            return {
                                success: true,
                                recordCount: result.record_count
                            };
                        } else {
                            console.warn('SQL function returned error:', result.error_message);
                        }
                    } else if (sqlError) {
                        console.warn('SQL function approach failed:', sqlError.message);
                    }
                }
            }
        } else if (countError) {
            console.warn('Count function failed:', countError.message);
        }

        // Fallback to JavaScript client approach
        console.log('Falling back to JavaScript client approach...');

        // Add retry logic for Supabase schema cache issues
        let uploadData = null;
        let selectError = null;
        const maxRetries = 3;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(`Attempt ${attempt}/${maxRetries}: Querying v_informed_upload...`);

            const result = await supabase
                .from('v_informed_upload')
                .select('*');

            uploadData = result.data;
            selectError = result.error;

            if (!selectError) {
                console.log(`Successfully queried v_informed_upload on attempt ${attempt}`);
                break;
            }

            console.warn(`Attempt ${attempt} failed:`, selectError.message);

            if (attempt < maxRetries) {
                console.log(`Waiting 2 seconds before retry...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        if (selectError) {
            console.error('Error selecting from v_informed_upload after all retries:', selectError);
            return {
                success: false,
                error: `Failed after ${maxRetries} attempts: ${selectError.message}`
            };
        }

        if (!uploadData || uploadData.length === 0) {
            console.log('No data found in v_informed_upload');
            return {
                success: true,
                recordCount: 0
            };
        }

        console.log(`Found ${uploadData.length} records in v_informed_upload`);

        // Transform the data to match tu_informed structure and handle NULL map_price
        const transformedData = uploadData.map(record => ({
            sku: record.sku,
            marketplace_id: record.marketplace_id,
            cost: record.cost,
            currency: record.currency,
            min_price: record.min_price,
            max_price: record.max_price,
            map_price: record.map_price || 0, // Replace NULL with 0
            listing_type: record.listing_type,
            strategy_id: record.strategy_id,
            managed: record.managed,
            dateadded: record.dateadded
        }));

        // Insert data in batches (reduced size to be gentler on Supabase)
        const batchSize = 500;
        let insertedCount = 0;

        for (let i = 0; i < transformedData.length; i += batchSize) {
            const batch = transformedData.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(transformedData.length / batchSize);

            console.log(`Inserting batch ${batchNumber}/${totalBatches} (${batch.length} records)`);

            // Add retry logic for insert operations
            let insertError = null;
            const maxInsertRetries = 3;

            for (let attempt = 1; attempt <= maxInsertRetries; attempt++) {
                const result = await supabase
                    .from('tu_informed')
                    .insert(batch);

                insertError = result.error;

                if (!insertError) {
                    console.log(`Batch ${batchNumber} inserted successfully on attempt ${attempt}`);
                    break;
                }

                console.warn(`Batch ${batchNumber} insert attempt ${attempt} failed:`, insertError.message);

                if (attempt < maxInsertRetries) {
                    console.log(`Waiting 2 seconds before retry...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            if (insertError) {
                console.error(`Error inserting batch ${batchNumber} after all retries:`, insertError);
                return {
                    success: false,
                    error: `Error inserting batch ${batchNumber} after ${maxInsertRetries} attempts: ${insertError.message}`
                };
            }

            insertedCount += batch.length;

            // Small delay to avoid overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log(`Successfully filled tu_informed with ${insertedCount} records`);

        return {
            success: true,
            recordCount: insertedCount
        };
    } catch (error) {
        console.error('Error filling tu_informed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Generate CSV export from tu_informed
 * @returns {Promise<Object>} - Result of the operation with CSV content
 */
async function generateCsvExport() {
    console.log('Generating CSV export from tu_informed');

    try {
        // Use the new function that exports in Informed's expected format
        const { data: csvContent, error } = await supabase.rpc('f_export_tu_informed_to_csv_informed_format');

        if (error) {
            console.error('Error generating CSV export:', error);
            return {
                success: false,
                error: error.message
            };
        }

        if (!csvContent) {
            return {
                success: false,
                error: 'No CSV content generated'
            };
        }

        console.log(`Generated CSV export with ${csvContent.split('\n').length - 1} data rows`);

        return {
            success: true,
            csvContent
        };
    } catch (error) {
        console.error('Error generating CSV export:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Upload CSV to Informed Repricer
 * @param {string} csvContent - The CSV content to upload
 * @returns {Promise<Object>} - Result of the upload operation
 */
async function uploadToInformed(csvContent) {
    console.log('Uploading CSV to Informed Repricer using /v1/feed endpoint');

    try {
        // Validate CSV content first
        if (!csvContent || typeof csvContent !== 'string') {
            throw new Error('Invalid CSV content provided');
        }

        // Check if CSV has proper headers
        const lines = csvContent.split('\n');
        if (lines.length < 2) {
            throw new Error('CSV content appears to be empty or invalid');
        }

        console.log(`Uploading to: ${INFORMED_UPLOAD_URL}`);
        console.log(`Using API key: ${INFORMED_API_KEY.substring(0, 10)}...`);
        console.log(`CSV content length: ${csvContent.length} characters`);
        console.log(`CSV lines count: ${lines.length}`);
        console.log(`CSV header: ${lines[0]}`);
        console.log(`CSV first data row: ${lines[1]}`);

        // Send raw CSV content with text/csv content type (per Informed API docs)
        const response = await fetch(INFORMED_UPLOAD_URL, {
            method: 'POST',
            headers: {
                'x-api-key': INFORMED_API_KEY,
                'Content-Type': 'text/csv',
                'Accept': 'application/json'
            },
            body: csvContent  // Send raw CSV content, not JSON
        });

        console.log(`Upload response: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Informed upload failed with status ${response.status}: ${errorText}`);
            return {
                success: false,
                error: `Upload failed with status ${response.status}: ${errorText}`
            };
        }

        const result = await response.json();
        console.log('Successfully uploaded CSV to Informed:', result);

        return {
            success: true,
            result
        };
    } catch (error) {
        console.error('Error uploading to Informed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Run the complete upload workflow
 * @returns {Promise<Object>} - Result of the complete workflow
 */
export async function runCompleteUploadWorkflow() {
    console.log('Starting complete Informed upload workflow');

    const results = {
        truncate: null,
        fill: null,
        export: null,
        upload: null
    };

    try {
        // Step 1: Truncate tu_informed
        console.log('Step 1: Truncating tu_informed');
        results.truncate = await truncateTuInformed();

        if (!results.truncate.success) {
            return {
                success: false,
                error: 'Failed to truncate tu_informed',
                results
            };
        }

        // Step 2: Fill tu_informed
        console.log('Step 2: Filling tu_informed');
        results.fill = await fillTuInformed();

        if (!results.fill.success) {
            return {
                success: false,
                error: 'Failed to fill tu_informed',
                results
            };
        }

        // Step 3: Generate CSV export
        console.log('Step 3: Generating CSV export');
        results.export = await generateCsvExport();

        if (!results.export.success) {
            return {
                success: false,
                error: 'Failed to generate CSV export',
                results
            };
        }

        // Step 4: Upload to Informed
        console.log('Step 4: Uploading to Informed');
        results.upload = await uploadToInformed(results.export.csvContent);

        if (!results.upload.success) {
            return {
                success: false,
                error: 'Failed to upload to Informed',
                results
            };
        }

        console.log('Complete Informed upload workflow completed successfully');

        return {
            success: true,
            message: 'Complete workflow completed successfully',
            results
        };
    } catch (error) {
        console.error('Error in complete upload workflow:', error);
        return {
            success: false,
            error: error.message,
            results
        };
    }
}

/**
 * Run individual steps of the workflow
 */
export async function truncateOnly() {
    return await truncateTuInformed();
}

export async function fillOnly() {
    return await fillTuInformed();
}

export async function exportOnly() {
    return await generateCsvExport();
}

export async function uploadOnly(csvContent) {
    return await uploadToInformed(csvContent);
}

// Main function for running the script directly
async function main() {
    try {
        console.log('Starting Informed upload workflow...');

        const result = await runCompleteUploadWorkflow();

        console.log('Upload workflow result:', result);

        if (result.success) {
            console.log('Upload workflow completed successfully.');
        } else {
            console.error('Upload workflow failed.');
        }
    } catch (error) {
        console.error('Error running upload workflow:', error);
    }
}

// Run the main function if this script is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    main();
}
