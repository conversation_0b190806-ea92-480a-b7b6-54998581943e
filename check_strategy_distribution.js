import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStrategyDistribution() {
    try {
        console.log('Checking strategy ID distribution...');
        
        // Check the current strategy distribution in tu_informed
        console.log('\n=== CURRENT STRATEGY DISTRIBUTION IN tu_informed ===');
        const { data: currentDist, error: currentError } = await supabase
            .from('tu_informed')
            .select('strategy_id, listing_type')
            .limit(1000);
            
        if (currentError) {
            console.error('Error fetching current distribution:', currentError);
        } else {
            const strategyCount = {};
            currentDist.forEach(row => {
                const key = `${row.strategy_id} (${row.listing_type})`;
                strategyCount[key] = (strategyCount[key] || 0) + 1;
            });
            
            console.log('Strategy distribution in first 1000 records:');
            Object.entries(strategyCount).forEach(([key, count]) => {
                console.log(`  ${key}: ${count} records`);
            });
        }
        
        // Check what's configured in v_sdasins
        console.log('\n=== STRATEGY CONFIGURATION IN v_sdasins ===');
        const { data: configData, error: configError } = await supabase
            .from('v_sdasins')
            .select('id, fba_informed_strategy_id, fbm_informed_strategy_id')
            .not('fba_informed_strategy_id', 'is', null)
            .limit(20);
            
        if (configError) {
            console.error('Error fetching config data:', configError);
        } else {
            console.log('Sample SDAINs with configured strategy IDs:');
            configData.forEach(row => {
                console.log(`  SDASIN ${row.id}: FBA=${row.fba_informed_strategy_id}, FBM=${row.fbm_informed_strategy_id}`);
            });
        }
        
        // Check unique strategy IDs in v_sdasins
        console.log('\n=== UNIQUE STRATEGY IDs IN v_sdasins ===');
        const { data: uniqueData, error: uniqueError } = await supabase.rpc('exec_sql', {
            sql_query: `
                SELECT 
                    fba_informed_strategy_id as strategy_id, 
                    'FBA' as type,
                    COUNT(*) as count
                FROM v_sdasins 
                WHERE fba_informed_strategy_id IS NOT NULL
                GROUP BY fba_informed_strategy_id
                
                UNION ALL
                
                SELECT 
                    fbm_informed_strategy_id as strategy_id, 
                    'FBM' as type,
                    COUNT(*) as count
                FROM v_sdasins 
                WHERE fbm_informed_strategy_id IS NOT NULL
                GROUP BY fbm_informed_strategy_id
                
                ORDER BY strategy_id, type;
            `
        });
        
        if (uniqueError) {
            console.error('Error fetching unique strategies:', uniqueError);
        } else {
            console.log('Unique strategy IDs configured in v_sdasins:');
            uniqueData.forEach(row => {
                console.log(`  Strategy ${row.strategy_id} (${row.type}): ${row.count} SDAINs`);
            });
        }
        
    } catch (error) {
        console.error('Error:', error);
    }
}

checkStrategyDistribution();
