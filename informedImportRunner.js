/**
 * Informed Import Runner
 *
 * This script executes the PowerShell script to import Informed Repricer data.
 * It can be called from the admin interface or scheduled as a task.
 */

import { exec } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * Run the PowerShell script to import Informed Repricer data
 * @returns {Promise<Object>} - Result of the import operation
 */
export async function runImport() {
    return new Promise((resolve, reject) => {
        // Get the path to the PowerShell script
        const scriptPath = path.join(__dirname, 'import_informed_data.ps1');

        // Get the database connection details from environment variables
        const pgUser = process.env.PGUSER || 'postgres';
        const pgDatabase = process.env.PGDATABASE || 'postgres';
        const pgHost = process.env.PGHOST || 'localhost';
        const pgPort = process.env.PGPORT || '5432';

        // Build the PowerShell command
        const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -pgUser "${pgUser}" -pgDatabase "${pgDatabase}" -pgHost "${pgHost}" -pgPort "${pgPort}" -silent -pgPasswordEnvVar "PGPASSWORD"`;

        console.log(`Executing command: ${command}`);

        // Execute the PowerShell script
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error executing PowerShell script: ${error.message}`);
                console.error(`stderr: ${stderr}`);

                try {
                    // Try to parse the JSON output
                    const result = JSON.parse(stdout);
                    reject(result);
                } catch (parseError) {
                    reject({
                        success: false,
                        message: error.message,
                        details: stderr || stdout
                    });
                }

                return;
            }

            if (stderr) {
                console.warn(`PowerShell script stderr: ${stderr}`);
            }

            try {
                // Look for JSON between JSON_START and JSON_END markers
                const jsonMatch = stdout.match(/JSON_START\s*([\s\S]*?)\s*JSON_END/);
                if (jsonMatch && jsonMatch[1]) {
                    try {
                        const result = JSON.parse(jsonMatch[1]);
                        resolve(result);
                    } catch (jsonError) {
                        console.error(`Error parsing JSON from PowerShell output: ${jsonError.message}`);
                        console.error(`JSON match: ${jsonMatch[1]}`);

                        resolve({
                            success: true,
                            message: 'Import completed, but JSON output could not be parsed',
                            details: stdout
                        });
                    }
                }
                // Fallback to looking for any JSON object in the output
                else {
                    const anyJsonMatch = stdout.match(/\{[\s\S]*\}/);
                    if (anyJsonMatch) {
                        try {
                            const result = JSON.parse(anyJsonMatch[0]);
                            resolve(result);
                        } catch (jsonError) {
                            console.error(`Error parsing JSON from PowerShell output: ${jsonError.message}`);

                            if (stdout.includes("Import completed successfully")) {
                                resolve({
                                    success: true,
                                    message: 'Import completed successfully',
                                    details: stdout
                                });
                            } else {
                                resolve({
                                    success: true,
                                    message: 'Import completed, but JSON output could not be parsed',
                                    details: stdout
                                });
                            }
                        }
                    }
                    // If no JSON found but success message exists
                    else if (stdout.includes("Import completed successfully")) {
                        resolve({
                            success: true,
                            message: 'Import completed successfully',
                            details: stdout
                        });
                    }
                    // No JSON and no success message
                    else {
                        console.error(`No JSON found in PowerShell output: ${stdout}`);

                        resolve({
                            success: true,
                            message: 'Import completed, but output could not be parsed',
                            details: stdout
                        });
                    }
                }
            } catch (parseError) {
                console.error(`Error processing PowerShell script output: ${parseError.message}`);
                console.error(`stdout: ${stdout}`);

                resolve({
                    success: true,
                    message: 'Import completed, but output could not be processed',
                    details: stdout
                });
            }
        });
    });
}

/**
 * Main function for running the script directly
 */
async function main() {
    try {
        console.log('Starting Informed Repricer data import...');

        const result = await runImport();

        console.log('Import result:', result);

        if (result.success) {
            console.log('Import completed successfully.');
        } else {
            console.error('Import failed:', result.message);
        }
    } catch (error) {
        console.error('Error running import:', error);
    }
}

// Run the main function if this script is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    main();
}
