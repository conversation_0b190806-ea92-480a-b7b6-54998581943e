import { exportOnly, uploadOnly } from './informedUploadHandler.js';

async function testUpload() {
    try {
        console.log('Testing corrected upload with raw CSV content...');
        
        // First get the CSV content
        console.log('Step 1: Exporting CSV...');
        const exportResult = await exportOnly();
        if (!exportResult.success) {
            console.error('Failed to export CSV:', exportResult.error);
            return;
        }
        
        console.log('CSV export successful');
        console.log('CSV length:', exportResult.csvContent.length);
        console.log('First 100 chars:', exportResult.csvContent.substring(0, 100));
        
        // Test the upload with raw CSV
        console.log('Step 2: Testing upload...');
        const uploadResult = await uploadOnly(exportResult.csvContent);
        console.log('Upload result:', JSON.stringify(uploadResult, null, 2));
        
        if (uploadResult.success) {
            console.log('✅ Upload successful with raw CSV format!');
        } else {
            console.log('❌ Upload failed:', uploadResult.error);
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

testUpload();
