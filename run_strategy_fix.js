import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function runStrategyFix() {
    try {
        console.log('Updating v_informed_upload_minimal to use correct strategy IDs...');
        
        // Read and execute the SQL
        const sql = fs.readFileSync('fix_strategy_ids.sql', 'utf8');
        
        // Split the SQL into individual statements
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i].trim();
            if (statement) {
                console.log(`Executing statement ${i + 1}/${statements.length}...`);
                
                const { data, error } = await supabase.rpc('exec_sql', {
                    sql_query: statement
                });
                
                if (error) {
                    console.error(`Error in statement ${i + 1}:`, error);
                    return;
                } else {
                    console.log(`Statement ${i + 1} executed successfully`);
                    if (data && data.length > 0) {
                        console.log('Result:', data);
                    }
                }
            }
        }
        
        console.log('✅ Strategy ID fix completed successfully!');
        
    } catch (error) {
        console.error('Error:', error);
    }
}

runStrategyFix();
