/**
 * Test script for Informed API
 * 
 * This script tests the connection to the Informed API and verifies that the API key is working.
 */

import axios from 'axios';

// Constants
const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const BASE_URL = 'https://api.informed.co';

// Test the API connection
async function testApiConnection() {
    console.log('Testing Informed API connection...');
    
    try {
        // Test the report request endpoint
        const response = await axios({
            method: 'GET',
            url: `${BASE_URL}/reports/requestReport`,
            params: {
                reportType: 'All_Fields'
            },
            headers: {
                'x-api-key': API_KEY,
                'Accept': 'application/json'
            }
        });
        
        console.log('API Response:', JSON.stringify(response.data, null, 2));
        console.log('API connection successful!');
        
        if (response.data && response.data.reportRequestID) {
            console.log(`Report request ID: ${response.data.reportRequestID}`);
            
            // Test the report status endpoint
            const statusResponse = await axios({
                method: 'GET',
                url: `${BASE_URL}/reports/requests/${response.data.reportRequestID}`,
                headers: {
                    'x-api-key': API_KEY,
                    'Accept': 'application/json'
                }
            });
            
            console.log('Status Response:', JSON.stringify(statusResponse.data, null, 2));
        }
    } catch (error) {
        console.error('API connection failed:', error.message);
        
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
            console.error('Response headers:', JSON.stringify(error.response.headers, null, 2));
        }
    }
}

// Run the test
testApiConnection();
